<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.manage.merchant.mapper.MerchantStoreBalanceChangeRecordMapper">

    <resultMap id="BaseResultMap" type="com.cosfo.manage.merchant.model.po.MerchantStoreBalanceChangeRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="change_balance" jdbcType="DECIMAL" property="changeBalance"/>
        <result column="after_change_balance" jdbcType="DECIMAL" property="afterChangeBalance"/>
        <result column="associated_order_no" jdbcType="VARCHAR" property="associatedOrderNo"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="proof" jdbcType="VARCHAR" property="proof"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">id, tenant_id, store_id, change_balance, after_change_balance, associated_order_no,
        operator_id, proof, remark, type, create_time, update_time</sql>


    <select id="list" parameterType="com.cosfo.manage.merchant.model.dto.balance.BalanceRecordDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM merchant_store_balance_change_record
        <where>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="startTime != null and endTime != null">
                and create_time between date_format(#{startTime},'%Y-%m-%d 00:00:00') and date_format(#{endTime},'%Y-%m-%d 23:59:59')
            </if>
            <if test="storeIdList != null and storeIdList.size() >0 ">
                and store_id in
                <foreach collection="storeIdList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY id DESC
    </select>
</mapper>
