
# 数据库配置
server:
  port: 80
spring:
  application:
    name: cosfo-manage
  datasource:
    hikari:
      minimum-idle: 3
      maximum-pool-size: 10
      max-lifetime: 30000   #不能小于30秒，否则默认回到1800秒
      connection-test-query: SELECT 1
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          url: ***********************************************************************************************************************
          username: wurth
          password: Wurth20250531
          driver-class-name: com.mysql.cj.jdbc.Driver
    driver-class-name: com.mysql.cj.jdbc.Driver
#    # 自定义数据源
    type: com.alibaba.druid.pool.DruidDataSource
#    druid 数据源专有配置
    initialSize: 5
    minIdle: 5
    maxActive: 80
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
  # redis配置
  redis:
    host: r-t4n7v3kot5slqys3uf.redis.singapore.rds.aliyuncs.com
    password: Wurth20250531
    port: 6379
    timeout: 6000
    database: 0
  # auth服务依赖
  authRedis:
    host: r-t4n7v3kot5slqys3uf.redis.singapore.rds.aliyuncs.com
    port: 6379
    password: Wurth20250531
    timeout: 5000
    database: 0
    jedis:
      pool:
        max-active: 5
        max-idle: 5
        min-idle: 5
        max-wait: 5

rocketmq:
  name-server: http://MQ_INST_1175642280696130_BZcBFTRh.ap-southeast-1.mq-internal.aliyuncs.com:8080
  producer:
    enable-msg-trace: off
    send-message-timeout: 10000
    access-key: LTAI5tL7BY68nEt96rUGQWny
    secret-key: ******************************
    group: GID_wurth-manage
  consumer:
    access-key: LTAI5tL7BY68nEt96rUGQWny
    secret-key: ******************************

redisson:
  address: r-t4n7v3kot5slqys3uf.redis.singapore.rds.aliyuncs.com:6379
  password: Wurth20250531
  type: STANDALONE
  enabled: true
  database: 0
  enable: true

tenant:
  # 是否开启租户模式
  enable: false
  # 需要排除的多租户的表
  exclusionTable:
    - "system_parameters"
  column: tenant_id

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://mse-6575e3110-nacos-ans.mse.aliyuncs.com:8848
    parameters:
      namespace: 7f8dc8f1-66a0-44e4-943c-48621954f072
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false

xm:
  oss:
    persistent-storage:
      bucketName: wurth-app-perm
      endpoint: oss-ap-southeast-1.aliyuncs.com
      innerEndpoint: oss-ap-southeast-1-internal.aliyuncs.com
      accessKeyId: LTAI5tL7BY68nEt96rUGQWny
      accessKeySecret: ******************************
      domain: ossperm.wuchetech.com
    temporary-storage:
      bucketName: wurth-app-temp
      endpoint: oss-ap-southeast-1.aliyuncs.com
      innerEndpoint: oss-ap-southeast-1-internal.aliyuncs.com
      accessKeyId: LTAI5tL7BY68nEt96rUGQWny
      accessKeySecret: ******************************
      domain: osstemp.wuchetech.com
  downloadcenter:
    consumer_group_suffix: cosfo-manage
    # tag前缀为tag_
    consumer_tag_suffix: cosfo-manage

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


H5:
  mall:
    url: https://wurthmall.cosfo.cn/index.html#/pages/loading/index?token=


nacos:
  config:
    server-addr: mse-6575e3110-nacos-ans.mse.aliyuncs.com:8848
    namespace: 65cacf65-df25-45b1-9197-241930618481

qiniu:
  access.key: IAM-gP3OtxMjGLK8VcHYRCVmu5RZl_tNTBNgAUBKKUil
  secret.key: Ip-oTDGZnZg2HPe1k4lChll-PF4KAoDabzZ_B-3t6meH
  default.bucket: wurth
  default.domain: https://cdn.wurthtech.com/