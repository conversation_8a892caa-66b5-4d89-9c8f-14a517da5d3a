package com.cosfo.manage.facade;

import com.alibaba.fastjson.JSON;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.manage.common.converter.AuthMenuPurviewMapper;
import com.cosfo.manage.tenant.model.dto.MenuPurviewDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.dto.AuthMenuPurviewDto;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.provider.AuthPurviewProvider;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2024/1/10 17:03
 * @Description:
 */
@Service
@Slf4j
public class AuthPurviewFacade {

    @DubboReference
    private AuthPurviewProvider authPurviewProvider;

    public static final Integer maxLevel = 4;


    /**
     * 根据菜单ID,查询菜单名称平铺列表
     *
     * @param tenantId
     * @param menuIds
     * @return
     */
    public List<String> listMenuNameTile(Long tenantId, List<Long> menuIds) {
        if (Objects.isNull(tenantId) || CollectionUtils.isEmpty(menuIds)) {
            throw new BizException("查詢菜單權限參數不可為空！");
        }
        List<AuthMenuPurviewDto> menuPurviewDtoList = RpcResponseUtil.handler(authPurviewProvider.getMenusTreeByMenuIds(SystemOriginEnum.COSFO_MANAGE, tenantId, menuIds));
        return buildMenuNameTile(menuPurviewDtoList);
    }

    private List<String> buildMenuNameTile(List<AuthMenuPurviewDto> treeMenu) {
        if (CollectionUtils.isEmpty(treeMenu)) {
            return Collections.emptyList();
        }
        // 将treeMenu中每一级的menuName拼接成字符串,一级菜单名>二级菜单名>三级菜单名
        List<String> menuNameList = new ArrayList<>();
        for (AuthMenuPurviewDto firstMenu : treeMenu) {
            if (CollectionUtils.isNotEmpty(firstMenu.getChildren())) {
                for (AuthMenuPurviewDto secMenu : firstMenu.getChildren()) {
                    if (CollectionUtils.isNotEmpty(secMenu.getChildren())) {
                        for (AuthMenuPurviewDto thirdMenu : secMenu.getChildren()) {
                            menuNameList.add(firstMenu.getMenuName() + ">" + secMenu.getMenuName() + ">" + thirdMenu.getPurviewName());
                        }
                    } else {
                        menuNameList.add(firstMenu.getMenuName() + ">" + secMenu.getMenuName());
                    }
                }
            } else {
                menuNameList.add(firstMenu.getMenuName());
            }
        }
        return menuNameList;

    }

    /**
     * 获取租户菜单树
     * @param systemOriginEnum
     * @param tenantId
     * @return
     */
    public List<MenuPurviewDTO> getAuthTenantMenuPurviewDto(SystemOriginEnum systemOriginEnum, Long tenantId) {
        DubboResponse<List<AuthMenuPurviewDto>> response = authPurviewProvider.getAuthTenantMenuPurviewDto(systemOriginEnum, tenantId);
        if (response.isSuccess()) {
            return AuthMenuPurviewMapper.INSTANCE.menuPurviewDtoTomenuPurviewDTOList(response.getData());
        }
        log.error("获取租户菜单权限失败,tenantId:{},response:{}", tenantId, JSON.toJSONString(response));
        throw new ProviderException(response.getMsg());
    }


    /**
     * 获取租户菜单列表
     * @param systemOriginEnum
     * @param tenantId
     * @return
     */
    public List<MenuPurviewDTO> getAuthTenantMenuPurview(SystemOriginEnum systemOriginEnum, Long tenantId) {
        DubboResponse<List<AuthMenuPurview>> response = authPurviewProvider.getAuthTenantMenuPurview(systemOriginEnum, tenantId);
        if (response.isSuccess()) {
            return AuthMenuPurviewMapper.INSTANCE.menuPurviewTomenuPurviewDTOList(response.getData());
        }
        log.error("获取租户菜单权限失败,tenantId:{},response:{}", tenantId, JSON.toJSONString(response));
        throw new ProviderException(response.getMsg());
    }

}
