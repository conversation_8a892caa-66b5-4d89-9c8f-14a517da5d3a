package com.cosfo.manage.facade.convert;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.facade.dto.ApplyAgentSkuDTO;
import com.cosfo.manage.facade.dto.CategoryDTO;
import com.cosfo.manage.facade.dto.SummerfarmProductInfoDTO;
import com.cosfo.manage.facade.dto.SupplierInfoDTO;
import com.cosfo.manage.facade.input.ProductStockChangeRecordQueryInput;
import net.summerfarm.manage.client.saas.req.ApplyAgentSkuReq;
import net.summerfarm.manage.client.saas.req.PageQuerySkuQuantityChangeRecordReq;
import net.summerfarm.manage.client.saas.resp.CategoryResp;
import net.summerfarm.manage.client.saas.resp.SummerfarmProductInfoResp;
import net.xianmu.authentication.client.dto.ShiroUser;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/2/6 11:22
 */
public class Convert {

    public static SummerfarmProductInfoDTO summerfarmProductInfoResp2Dto(SummerfarmProductInfoResp resp) {

        if (resp == null) {
            return null;
        }
        SummerfarmProductInfoDTO summerfarmProductInfoDTO = new SummerfarmProductInfoDTO();
        summerfarmProductInfoDTO.setSkuId(resp.getSkuId());
        summerfarmProductInfoDTO.setAfterSaleUnit(resp.getAfterSaleUnit());
        summerfarmProductInfoDTO.setMaxAfterSaleAmount(resp.getMaxAfterSaleAmount());
        return summerfarmProductInfoDTO;
    }

    public static PageQuerySkuQuantityChangeRecordReq productStockChangeRecord2Req(ProductStockChangeRecordQueryInput input) {

        if (input == null) {
            return null;
        }
        PageQuerySkuQuantityChangeRecordReq pageQuerySkuQuantityChangeRecordReq = new PageQuerySkuQuantityChangeRecordReq();
        pageQuerySkuQuantityChangeRecordReq.setPageIndex(input.getPageIndex());
        pageQuerySkuQuantityChangeRecordReq.setPageSize(input.getPageSize());
        pageQuerySkuQuantityChangeRecordReq.setSkuId(input.getSkuId());
        pageQuerySkuQuantityChangeRecordReq.setWarehouseNo(input.getWarehouseNo());
        pageQuerySkuQuantityChangeRecordReq.setStockTypeList(input.getStockTypeList());
        pageQuerySkuQuantityChangeRecordReq.setPdName(input.getPdName());
        pageQuerySkuQuantityChangeRecordReq.setChangeTypeNameList(input.getChangeTypeNameList());
        pageQuerySkuQuantityChangeRecordReq.setStartTime(input.getStartTime());
        pageQuerySkuQuantityChangeRecordReq.setEndTime(input.getEndTime());
        pageQuerySkuQuantityChangeRecordReq.setPermissionSkuIdList(input.getPermissionSkuIdList());
        pageQuerySkuQuantityChangeRecordReq.setTenantId(input.getTenantId());
        return pageQuerySkuQuantityChangeRecordReq;
    }


    /**
     * 转化为LoginContextInfoDTO
     *
     * @param shiroUser
     * @return
     */
    public static LoginContextInfoDTO convertToLoginContextInfoDTO(ShiroUser shiroUser){

        if (shiroUser == null) {
            return null;
        }
        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(shiroUser.getTenantId());
        loginContextInfoDTO.setPhone(shiroUser.getPhone());
        loginContextInfoDTO.setAuthUserId(shiroUser.getId());
        loginContextInfoDTO.setSystemOrigin(shiroUser.getSystemOrigin());
        return loginContextInfoDTO;
    }
}
