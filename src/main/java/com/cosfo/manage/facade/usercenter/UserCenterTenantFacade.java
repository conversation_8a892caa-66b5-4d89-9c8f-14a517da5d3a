package com.cosfo.manage.facade.usercenter;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.cache.InMemoryCache;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.tenant.provider.TenantQueryProvider;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterTenantFacade {

    @DubboReference
    private TenantQueryProvider tenantQueryProvider;
    /**
     * 获取租户信息
     *
     * @param tenantIds
     * @return
     */
    public List<TenantResultResp> getTenantsByIds(List<Long> tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds)) {
            return Collections.emptyList();
        }
        DubboResponse<List<TenantResultResp>> response = tenantQueryProvider.getTenantsByIds(tenantIds);
        if (!response.isSuccess()) {
            log.warn("获取租户信息失败, msg = {}", JSON.toJSONString(response));
            throw new BizException("获取租户信息失败");
        }

        return response.getData();
    }


    /**
     * 获取租户信息
     *
     * @param tenantId
     * @return
     */
    @InMemoryCache
    public TenantResultResp getTenantById(Long tenantId) {
        DubboResponse<TenantResultResp> response = tenantQueryProvider.getTenantById(tenantId);
        if (!response.isSuccess()) {
            log.warn("获取租户信息失败, msg = {}", JSON.toJSONString(response));
            throw new BizException("获取租户信息失败");
        }

        return response.getData();
    }


    /**
     * 获取租户企业信息
     *
     * @param tenantId
     * @return
     */
    public TenantAndBusinessInfoResultResp getTenantAndCompanyById(Long tenantId) {
        DubboResponse<TenantAndBusinessInfoResultResp> response = tenantQueryProvider.getTenantAndCompany(tenantId);
        if (!response.isSuccess()) {
            log.warn("获取租户信息失败, msg = {}", JSON.toJSONString(response));
            throw new BizException("获取租户信息失败");
        }

        return response.getData();
    }

    /**
     * 根据条件获取租户企业信息
     *
     * @param req
     * @return
     */
    public List<TenantAndBusinessInfoResultResp> getTenantAndCompanyByQuery(TenantQueryReq req) {
        DubboResponse<List<TenantAndBusinessInfoResultResp>> response = tenantQueryProvider.getTenantAndCompanyList(req);
        if (!response.isSuccess()) {
            log.warn("获取租户信息失败, msg = {}", JSON.toJSONString(response));
            throw new BizException("获取租户信息失败");
        }

        return response.getData();
    }

    /**
     * 获取租户分页信息
     *
     * @param tenantQueryReq
     * @param pageQueryReq
     * @return
     */
    public PageInfo<TenantAndBusinessInfoResultResp> getTenantAndCompanyPage(TenantQueryReq tenantQueryReq, PageQueryReq pageQueryReq) {
        DubboResponse<PageInfo<TenantAndBusinessInfoResultResp>> response = tenantQueryProvider.getTenantsPage(tenantQueryReq, pageQueryReq);
        if (!response.isSuccess()) {
            log.warn("获取租户信息失败, msg = {}", JSON.toJSONString(response));
            throw new BizException("获取租户信息失败");
        }
        return response.getData();
    }

    /**
     * 根据查询条件获取租户信息列表
     *
     * @param req
     * @return
     */
    public List<TenantResultResp> getTenantsByQuery(TenantQueryReq req) {
        DubboResponse<List<TenantResultResp>> response = tenantQueryProvider.getTenants(req);
        if (!response.isSuccess()) {
            log.warn("获取租户信息失败, msg = {}", JSON.toJSONString(response));
            throw new BizException("获取租户信息失败");
        }

        return response.getData();
    }


    /**
     * 获取门店下的所有门店数
     *
     * @param tenantId
     * @return
     */
    public Integer getStoreCount(Long tenantId) {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq ();
        req.setTenantId (tenantId);
        DubboResponse<Integer> response = tenantQueryProvider.getStoreCount(req);
        if (!response.isSuccess()) {
            log.warn("获取租户门店数量失败, msg = {}", JSON.toJSONString(response));
            throw new BizException("獲取租戶門店數量");
        }

        return response.getData();
    }
}
