package com.cosfo.manage.facade.usercenter;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.util.StringUtils;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreBatchCreateProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreBatchImportResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStorePageResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import net.xianmu.usercenter.client.regional.provider.RegionalOrganizationQueryProvider;
import net.xianmu.usercenter.client.regional.resp.RegionalOrganizationResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserCenterMerchantStoreFacade {

    @DubboReference
    private MerchantStoreQueryProvider merchantStoreQueryProvider;
    @DubboReference
    private MerchantStoreCommandProvider merchantStoreCommandProvider;

    @DubboReference(timeout = 1000 * 30)
    private MerchantStoreBatchCreateProvider merchantStoreBatchCreateProvider;

    /**
     * 获取门店信息
     *
     * @param id
     * @return
     */
    public MerchantStoreResultResp getMerchantStoreById(Long id) {
        DubboResponse<MerchantStoreResultResp> response = merchantStoreQueryProvider.getMerchantStoreById(id);
        if (!response.isSuccess()) {
            throw new BizException("獲取門店資訊失敗");
        }
        return response.getData();
    }
    /**
     * 获取门店信息
     *
     * @return
     */
    public MerchantStorePageResultResp getMerchantStoreByCode(Long tenantId, String code) {
        MerchantStorePageQueryReq queryReq = new MerchantStorePageQueryReq ();
        queryReq.setTenantId (tenantId);
        queryReq.setStoreNo (code);
        PageQueryReq pageQueryReq = new PageQueryReq ();
        pageQueryReq.setPageIndex (1);
        pageQueryReq.setPageSize (1);
        DubboResponse<PageInfo<MerchantStorePageResultResp>> response = merchantStoreQueryProvider.getMerchantStorePage (queryReq, pageQueryReq);
        if (!response.isSuccess()) {
            throw new BizException("獲取門店資訊失敗");
        }
        if (response.getData()!=null && CollectionUtil.isNotEmpty (response.getData().getList ())) {
            return response.getData().getList ().get (0);
        }
        return null;
    }

    /**
     * 分页查询门店信息
     *
     * @param queryReq
     * @param pageQueryReq
     * @return
     */
    public PageInfo<MerchantStorePageResultResp> getMerchantStorePage(MerchantStorePageQueryReq queryReq, PageQueryReq pageQueryReq) {
        DubboResponse<PageInfo<MerchantStorePageResultResp>> response = merchantStoreQueryProvider.getMerchantStorePage(queryReq, pageQueryReq);
        if (!response.isSuccess()) {
            throw new BizException("分頁查詢門店資訊失敗");
        }
        return response.getData();
    }
    /**
     * 门店分页列表（用于分组新增门店时使用）
     *
     * @param queryReq
     * @param pageQueryReq
     * @return
     */
    public PageInfo<MerchantStoreAndAddressResultResp> getMerchantStoreAndAddressPage(MerchantStorePageQueryReq queryReq, PageQueryReq pageQueryReq) {
        DubboResponse<PageInfo<MerchantStoreAndAddressResultResp>> response = merchantStoreQueryProvider.getMerchantStoreAndAddressPage(queryReq, pageQueryReq);
        if (!response.isSuccess()) {
            throw new BizException("查詢客戶分頁列表失敗");
        }
        return response.getData();
    }

    /**
     * 获取门店列表
     *
     * @param ids
     * @return
     */
    public List<MerchantStoreResultResp> getMerchantStoreList(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        DubboResponse<List<MerchantStoreResultResp>> response = merchantStoreQueryProvider.getMerchantStoreByIds(ids);
        if (!response.isSuccess()) {
            throw new BizException("獲取門店資訊失敗");
        }
        return response.getData();
    }

    /**
     * 获取门店信息列表
     *
     * @param merchantStoreQueryReq
     * @return
     */
    public List<MerchantStoreResultResp> getMerchantStoreList(MerchantStoreQueryReq merchantStoreQueryReq) {
        DubboResponse<List<MerchantStoreResultResp>> response = merchantStoreQueryProvider.getMerchantStores(merchantStoreQueryReq);
        if (!response.isSuccess()) {
            throw new BizException("獲取門店資訊失敗");
        }
        return response.getData();
    }

    /**
     * 更新门店（仅更新门店表信息）
     *
     * @param merchantStoreCommandReq
     * @return
     */
    public Boolean updateMerchantStore(MerchantStoreCommandReq merchantStoreCommandReq) {
        DubboResponse<Boolean> response = merchantStoreCommandProvider.updateMerchantStore(SystemOriginEnum.COSFO_MANAGE, merchantStoreCommandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"更新門店資訊失敗");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }


    /**
     * 创建门店（包含门店、账户、地址、联系人、分组）
     *
     * @param merchantStoreDomainCommandReq
     * @return
     */
    public Long createMerchantStoreInfo(MerchantStoreDomainCommandReq merchantStoreDomainCommandReq) {
        //将对象转为json格式
        String json = JSONObject.toJSONString(merchantStoreDomainCommandReq);
        System.out.println(json);
        DubboResponse<Long> response = merchantStoreCommandProvider.createMerchantStoreInfo(SystemOriginEnum.COSFO_MANAGE, merchantStoreDomainCommandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"創建門店詳細資訊失敗");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 批量创建门店（包含门店、账户、地址、联系人、分组）
     *
     * @param reqs
     * @return
     */
    public List<MerchantStoreBatchImportResp> createMerchantStoreInfoBatch(List<MerchantStoreDomainCommandReq> reqs) {
        DubboResponse<List<MerchantStoreBatchImportResp>> response = merchantStoreBatchCreateProvider.createMerchantStoreInfoBatch (SystemOriginEnum.COSFO_MANAGE.getType (), reqs);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"創建門店詳細資訊失敗");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }


    /**
     * 更新门店（包含门店、账户、地址、联系人、分组）
     *
     * @param merchantStoreDomainCommandReq
     * @return
     */
    public Boolean updateMerchantStoreInfo(MerchantStoreDomainCommandReq merchantStoreDomainCommandReq) {
        //将对象转为json格式
        String json = JSONObject.toJSONString(merchantStoreDomainCommandReq);
        System.out.println(json);
        DubboResponse<Boolean> response = merchantStoreCommandProvider.updateMerchantStoreInfo(SystemOriginEnum.COSFO_MANAGE, merchantStoreDomainCommandReq);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"更新門店詳細資訊失敗");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }
}
