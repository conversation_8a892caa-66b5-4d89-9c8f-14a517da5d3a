package com.cosfo.manage.facade.usercenter;

import com.cosfo.manage.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressCommandProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-31
 * @Description:
 */
@Service
@Slf4j
public class UserCenterMerchantAddressFacade {

    @DubboReference
    private MerchantAddressCommandProvider merchantAddressCommandProvider;

    @DubboReference
    private MerchantAddressQueryProvider merchantAddressQueryProvider;

    /**
     * 根据指定参数查询门店地址列表
     * @param
     * @return
     */
    public List<MerchantAddressResultResp> getMerchantAddressList(MerchantAddressQueryReq req){
        DubboResponse<List<MerchantAddressResultResp>> response = merchantAddressQueryProvider.getMerchantAddressList(req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"查询门店地址列表失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 查询指定租户下所有的省、市、区
     * @param
     * @return
     */
    public List<String> getConcatAddress(Long tenantId){
        DubboResponse<List<String>> response = merchantAddressQueryProvider.getConcatAddress(tenantId);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"查询指定租户下所有的省、市、区失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

    /**
     * 删除地址信息
     */
    public Boolean remove(MerchantAddressCommandReq req) {
        DubboResponse<Boolean> response = merchantAddressCommandProvider.remove(SystemOriginEnum.COSFO_MANAGE, req);
        if (!response.isSuccess()) {
            String errorMsg = StringUtils.builderErrorMsg(response,"删除地址信息失败");
            throw new BizException(errorMsg);
        }
        return response.getData();
    }

}
