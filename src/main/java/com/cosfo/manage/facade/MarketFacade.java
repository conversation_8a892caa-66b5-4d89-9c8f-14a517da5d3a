package com.cosfo.manage.facade;

import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cofso.item.client.provider.MarketProvider;
import com.cofso.item.client.req.MarketDeleteReq;
import com.cofso.item.client.req.MarketDetailQueryReq;
import com.cofso.item.client.req.MarketItemInfoQueryFlagReq;
import com.cofso.item.client.req.MarketQueryReq;
import com.cofso.item.client.resp.*;
import com.cofso.page.PageResp;
import com.cosfo.manage.cache.InMemoryCache;
import com.cosfo.manage.common.constant.XianmuSupplyTenant;
import com.cosfo.manage.common.exception.FacadeExceptionUtil;
import com.cosfo.manage.market.converter.MarketFacadeConvert;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.model.vo.BatchOnSaleResultVO;
import com.cosfo.manage.market.model.vo.MarketSpuVO;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @author: monna.chen
 * @Date: 2023/4/27 17:53
 * @Description:
 */
@Service
@Slf4j
public class MarketFacade {
    @DubboReference
    private MarketProvider marketProvider;

//    public PageInfo<MarketItemInfoDTO> queryMarketItemList(MarketItemQueryDTO queryDTO) {
//        DubboResponse<PageResp<MarketItemInfoResp>> dubboResponse = marketProvider.queryMarketItemList(MarketFacadeConvert.INSTANCE.convert2Query(queryDTO));
//        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
//        return MarketFacadeConvert.INSTANCE.convert2ItemDtoPage(dubboResponse.getData());
//    }

//    /**
//     * 查询marketItemMap
//     *
//     * @param queryDTO
//     * @return
//     */
//    public Map<Long, MarketItemInfoDTO> queryMarketItemMap(MarketItemQueryDTO queryDTO) {
//        PageInfo<MarketItemInfoDTO> pageInfo = queryMarketItemList(queryDTO);
//        List<MarketItemInfoDTO> marketItemList = pageInfo.getList();
//        return marketItemList.stream().collect(Collectors.toMap(MarketItemInfoDTO::getItemId, Function.identity(), (k1, k2) -> k2));
//    }


    /**
     * 根据skuId查询鲜沐商品信息
     * 鲜沐商品同步到saas的market_item表，tenantId=1
     *
     * @param skuIds
     * @return
     */
    public Map<Long, MarketItemInfoResp> queryQuotationMarketItemMap(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }
        DubboResponse<List<MarketItemInfoResp>> dubboResponse = marketProvider.queryMarketItemInfoBySkuIds(XianmuSupplyTenant.TENANT_ID, skuIds);
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        List<MarketItemInfoResp> list = dubboResponse.getData();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(MarketItemInfoResp::getSkuId, Function.identity(), (V1, V2) -> V1));
    }

    public MarketSpuVO getMarketDetail(Long tenantId, Long marketId) {
        DubboResponse<MarketDetailResp> dubboResponse = marketProvider.getMarketDetail(MarketDetailQueryReq.builder()
                .marketId(marketId)
                .tenantId(tenantId)
                .build());
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return MarketFacadeConvert.INSTANCE.convert2MarketSpuVo(dubboResponse.getData());
    }

    public MarketAddResDTO addMarket(MarketSpuInput spuInput) {
        DubboResponse<AddMarketResp> dubboResponse = marketProvider.addMarket(MarketFacadeConvert.INSTANCE.convert2InputReq(spuInput));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return MarketFacadeConvert.INSTANCE.convert2Res(dubboResponse.getData());
    }

    public void updateMarket(MarketSpuInput spuInput) {
        DubboResponse<Boolean> dubboResponse = marketProvider.updateMarket(MarketFacadeConvert.INSTANCE.convert2InputReq(spuInput));
        FacadeExceptionUtil.executeFaceException(dubboResponse, "未更新成功！");
    }

    public PageInfo<MarketSpuVO> queryMarketListPage(MarketQueryInput queryInput) {
        MarketQueryReq marketQueryReq = MarketFacadeConvert.INSTANCE.convert2QueryReq(queryInput);
        MarketItemInfoQueryFlagReq marketItemInfoQueryFlagReq = new MarketItemInfoQueryFlagReq();
        marketItemInfoQueryFlagReq.setStockFlag(true);
        marketItemInfoQueryFlagReq.setCategoryIdFlag(false);
        marketItemInfoQueryFlagReq.setPriceRangeFlag(true);
        marketItemInfoQueryFlagReq.setClassificationIdFlag(false);
        marketItemInfoQueryFlagReq.setUnitFlag(true);
        marketQueryReq.setMarketItemInfoQueryFlagReq(marketItemInfoQueryFlagReq);
        DubboResponse<PageResp<MarketResp>> dubboResponse = marketProvider.queryMarketListPage(marketQueryReq);
        if (!dubboResponse.isSuccess()) {
            throw new ProviderException(dubboResponse.getMsg());
        }
        return MarketFacadeConvert.INSTANCE.convert2MarketPage(dubboResponse.getData());
    }

    public void deleteMarket(Long tenantId, Long marketId) {
        DubboResponse<Boolean> dubboResponse = marketProvider.deleteMarket(MarketDeleteReq.builder()
                .tenantId(tenantId)
                .id(marketId)
                .build());
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        if (Boolean.FALSE.equals(dubboResponse.getData())) {
            throw new ProviderException("未删除成功！");
        }
    }

    public MarketItemDTO getMarketItemDetail(Long tenantId, Long itemId) {
        DubboResponse<MarketItemInfoResp> dubboResponse = marketProvider.getMarketItemDetail(MarketDetailQueryReq.builder()
                .tenantId(tenantId)
                .marketItemId(itemId)
                .build());
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return MarketFacadeConvert.INSTANCE.convert2MarketItemDto(dubboResponse.getData());
    }

    public Long addMarketItem(MarketItemInput marketItemInput) {
        marketItemInput.setGoodsType (GoodsTypeEnum.NO_GOOD_TYPE.getCode ());
        DubboResponse<Long> dubboResponse = marketProvider.addMarketItem(MarketFacadeConvert.INSTANCE.convert2Req(marketItemInput));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return dubboResponse.getData();
    }

    public Boolean updateMarketItem(MarketItemInput marketItemInput) {
        marketItemInput.setGoodsType (GoodsTypeEnum.NO_GOOD_TYPE.getCode ());
        DubboResponse<Boolean> dubboResponse = marketProvider.updateMarketItem(MarketFacadeConvert.INSTANCE.convert2Req(marketItemInput));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return dubboResponse.getData();
    }

    public void deleteMarketItem(Long tenantId, Long itemId) {
        DubboResponse<Boolean> dubboResponse = marketProvider.deleteMarketItem(MarketDeleteReq.builder()
                .tenantId(tenantId)
                .marketItemId(itemId)
                .build());
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        if (Boolean.FALSE.equals(dubboResponse.getData())) {
            throw new ProviderException("未刪除成功！");
        }
    }

    public void changeOnSale(MarketItemOnSaleInput input) {
        DubboResponse<Boolean> dubboResponse = marketProvider.changeOnSale(MarketFacadeConvert.INSTANCE.convert2Req(input));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        if (Boolean.FALSE.equals(dubboResponse.getData())) {
            throw new ProviderException("未更新成功！");
        }
    }

    public BatchOnSaleResultVO batchChangOnSale(MarketItemOnSaleInput input) {
        DubboResponse<BatchOnSaleResultResp> dubboResponse = marketProvider.batchChangOnSale(MarketFacadeConvert.INSTANCE.convert2Req(input));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return MarketFacadeConvert.INSTANCE.convert2ResultVO(dubboResponse.getData());
    }

    public BatchOnSaleResultVO batchChangOnSaleIncludeAllStatus(MarketItemOnSaleInput soldOut, MarketItemOnSaleInput onSale) {
        if (soldOut == null && onSale == null) {
            return new BatchOnSaleResultVO();
        }
        DubboResponse<BatchOnSaleResultResp> dubboResponse = marketProvider.batchChangOnSaleIncludeAllStatus(MarketFacadeConvert.INSTANCE.convert2Req(soldOut), MarketFacadeConvert.INSTANCE.convert2Req(onSale));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return MarketFacadeConvert.INSTANCE.convert2ResultVO(dubboResponse.getData());
    }

    public MarketItemDTO queryItemPriceStrategy(Long tenantId, Long itemId) {
        DubboResponse<MarketItemInfoResp> dubboResponse = marketProvider.queryItemPriceStrategy(MarketDetailQueryReq.builder()
                .marketItemId(itemId)
                .tenantId(tenantId)
                .build());
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return MarketFacadeConvert.INSTANCE.convert2MarketItemDto(dubboResponse.getData());
    }

    public void updatePriceStrategy(MarketItemPriceStrategyInput input) {
        DubboResponse<Boolean> dubboResponse = marketProvider.updatePriceStrategy(MarketFacadeConvert.INSTANCE.convert2Req(input));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        if (Boolean.FALSE.equals(dubboResponse.getData())) {
            throw new ProviderException("未更新成功！");
        }
    }

    public List<MarketItemPriceStrategyUpdateResultResp> batchUpdateTenantTargetTypePriceStrategy(List<MarketItemPriceStrategyInput> inputs) {
        DubboResponse<List<MarketItemPriceStrategyUpdateResultResp>> dubboResponse = marketProvider.batchUpdateTenantTargetTypePriceStrategy(inputs.stream().map(input -> MarketFacadeConvert.INSTANCE.convert2Req(input)).collect(Collectors.toList()));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        if (Boolean.FALSE.equals(dubboResponse.isSuccess())) {
            throw new ProviderException("未更新成功！");
        }
        return dubboResponse.getData();
    }

    /**
     * 查询set（marekt_item.brandname）
     *
     * @return
     */
    @InMemoryCache
    public Set<String> queryBrandNameByTenantId(Long tenantId) {
        DubboResponse<Set<String>> dubboResponse = marketProvider.queryBrandNameByTenantId(tenantId);
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return dubboResponse.getData ();
    }

}
