package com.cosfo.manage.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cofso.item.client.enums.PriceTargetTypeEnum;
import com.cofso.item.client.provider.CostPriceProvider;
import com.cofso.item.client.provider.MarketProvider;
import com.cofso.item.client.provider.PriceProvider;
import com.cofso.item.client.req.*;
import com.cofso.item.client.resp.*;
import com.cofso.item.client.enums.MarketItemUnfairPriceStrategyEnum;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.exception.FacadeExceptionUtil;
import com.cosfo.manage.facade.convert.PriceFacadeConvert;
import com.cosfo.manage.market.converter.MarketFacadeConvert;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.model.vo.CostPriceRangeVO;
import com.cosfo.manage.market.model.vo.MarketSpuVO;
import com.cosfo.manage.market.model.vo.PriceDetailVO;
import com.cosfo.manage.market.model.vo.PriceStrategyRangeVO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAddressDTO;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;


/**
 * @author: monna.chen
 * @Date: 2023/4/27 17:53
 * @Description:
 */
@Service
@Slf4j
public class PriceFacade {
    @DubboReference
    private PriceProvider priceProvider;
    @DubboReference
    private CostPriceProvider costPriceProvider;

    public void batchUpdatePriceStrategy(Long tenantId, PriceStrategyFloatingRangeDTO dto) {
        PriceStrategyFloatingRangeReq req = PriceFacadeConvert.INSTANCE.convert2PriceStrategyFloatingRangeReq (dto);
        req.setTenantId (tenantId);
        DubboResponse<Void> response = priceProvider.batchUpdatePriceStrategy (req);
        if (!response.isSuccess ()) {
            throw new DefaultServiceException (response.getMsg ());
        }
    }

    public PriceStrategyRangeVO batchQueryPriceStrategy(Long tenantId, List<Long> marketItemIds) {
        DubboResponse<PriceStrategyRangeResp> response = priceProvider.batchQueryPriceStrategy (tenantId, marketItemIds);
        if (!response.isSuccess ()) {
            throw new DefaultServiceException (response.getMsg ());
        }
        PriceStrategyRangeResp data = response.getData ();
        return PriceFacadeConvert.INSTANCE.convert2PriceStrategyRangeVO (data);
    }

    public BigDecimal queryMaxCostPrice(Long tenantId, Long skuId, List<MerchantStoreAddressDTO> addressDtos) {
        MaxCostPriceQueryReq req = new MaxCostPriceQueryReq ();
        req.setTenantId (tenantId);
        req.setSkuId (skuId);
        List<MerchantAddressReq> addressReqs = addressDtos.stream ().filter (distinctByKey (person -> person.getArea ())).collect (Collectors.toList ()).stream ().map (e -> {
            MerchantAddressReq addressReq = new MerchantAddressReq ();
            addressReq.setProvince (e.getProvince ());
            addressReq.setCity (e.getCity ());
            addressReq.setArea (e.getArea ());
            addressReq.setCityId (e.getCityId ());
            return addressReq;
        }).collect (Collectors.toList ());
        req.setAddressReqs (addressReqs);
        DubboResponse<BigDecimal> response = priceProvider.queryMaxCostPrice (req);
        if (!response.isSuccess ()) {
            throw new DefaultServiceException (response.getMsg ());
        }
        return response.getData ();
    }

    //agentskumapping.agentskuid
    public List<CostPriceRangeVO> queryCostPriceRange(Set<Long> skuIds, Long tenantId) {
        if (CollectionUtil.isEmpty (skuIds)) {
            return Collections.emptyList ();
        }
        List<CostPriceQueryRangeReq> reqs = skuIds.stream ().map (skuId -> {
            CostPriceQueryRangeReq req = new CostPriceQueryRangeReq ();
            req.setSkuId (skuId);
            return req;
        }).collect (Collectors.toList ());
        DubboResponse<List<CostPriceRangeResultResp>> response = costPriceProvider.queryCostPriceRange (reqs, tenantId);
        if (!response.isSuccess ()) {
            throw new DefaultServiceException (response.getMsg ());
        }
        List<CostPriceRangeResultResp> data = response.getData ();
        return data.stream ().map (e -> {
            CostPriceRangeVO vo = new CostPriceRangeVO ();
            vo.setSkuId (e.getSkuId ());
            vo.setMinPrice (e.getMinPrice ());
            vo.setMaxPrice (e.getMaxPrice ());
            return vo;
        }).collect (Collectors.toList ());
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<> ();
        return t -> map.putIfAbsent (keyExtractor.apply (t), Boolean.TRUE) == null;
    }

    /**
     * 默认倒挂规则查询
     *
     * @return
     */
    public MarketItemUnfairPriceStrategyEnum.StrategyValueEnum queryDefaultUnfairPriceStrategy(Long tenantId) {
        DubboResponse<MarketItemUnfairPriceStrategyEnum.StrategyValueEnum> response = priceProvider.queryDefaultUnfairPriceStrategy (tenantId);
        if (!response.isSuccess ()) {
            throw new DefaultServiceException (response.getMsg ());
        }
        return response.getData ();
    }

    /**
     * 设置默认挂规则
     *
     * @return
     */
    public void upsertDefaultUnfairPriceStrategy(Long tenantId, Integer strategyValue) {
        MarketItemUnfairPriceStrategyEnum.StrategyValueEnum strategyValueEnum = MarketItemUnfairPriceStrategyEnum.StrategyValueEnum.getByCode (strategyValue);
        if (ObjectUtil.isNull (strategyValue)) {
            throw new DefaultServiceException ("價格倒挂規則值不合法");
        }
        DubboResponse<Void> response = priceProvider.upsertDefaultUnfairPriceStrategy (tenantId, strategyValueEnum);
        if (!response.isSuccess ()) {
            throw new DefaultServiceException (response.getMsg ());
        }
    }
}
