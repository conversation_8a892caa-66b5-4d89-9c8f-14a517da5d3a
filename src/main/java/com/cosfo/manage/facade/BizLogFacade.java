package com.cosfo.manage.facade;

import com.cosfo.manage.bizlog.convert.BizLogConvert;
import com.cosfo.manage.bizlog.model.dto.BizLogQueryDTO;
import com.cosfo.manage.bizlog.model.vo.BizLogListVO;
import com.cosfo.manage.common.exception.FacadeExceptionUtil;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.client.provider.BizLogCommandProvider;
import net.summerfarm.common.client.provider.BizLogQueryProvider;
import net.summerfarm.common.client.req.bizlog.SaveBizLogRecordReq;
import net.summerfarm.common.client.resp.bizlog.BizLogListResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/12/26 20:40
 * @Description:
 */
@Service
public class BizLogFacade {
    @DubboReference
    private BizLogQueryProvider bizLogQueryProvider;
    @DubboReference
    private BizLogCommandProvider bizLogCommandProvider;

    public PageInfo<BizLogListVO> listBizLog(BizLogQueryDTO queryDTO) {
        if (Objects.isNull(queryDTO)) {
            throw new BizException("查詢日誌參數為空！");
        }
        DubboResponse<PageInfo<BizLogListResp>> dubboResponse = bizLogQueryProvider.listBizLog(BizLogConvert.convert2QueryReq(queryDTO));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return BizLogConvert.convert2BizLogPage(dubboResponse.getData());
    }

    public void createTenantHistoryRecord(SaveBizLogRecordReq saveRecordReq) {
        bizLogCommandProvider.saveBizLogRecord(saveRecordReq);
    }

    public void saveBizLog(SaveBizLogRecordReq saveRecordReq) {
        bizLogCommandProvider.saveBizLogRecord(saveRecordReq);
    }

}
