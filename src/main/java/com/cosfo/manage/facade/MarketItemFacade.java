package com.cosfo.manage.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.req.MarketItemCommonQueryReq;
import com.cofso.item.client.req.MarketItemInfoQueryFlagReq;
import com.cofso.item.client.resp.BatchChangNoGoodsSupplyPriceResp;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.page.PageResp;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.exception.FacadeExceptionUtil;
import com.cosfo.manage.market.converter.MarketConvert;
import com.cosfo.manage.market.converter.MarketFacadeConvert;
import com.cosfo.manage.market.model.dto.ItemNoGoodsSupplyPriceExcelDataInput;
import com.cosfo.manage.market.model.dto.MarketItemInfoDTO;
import com.cosfo.manage.market.model.dto.MarketItemQueryDTO;
import com.cosfo.manage.market.model.vo.BatchChangNoGoodsSupplyPriceResultVO;
import com.cosfo.manage.market.model.vo.MarketItemInfoPageVO;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Set;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MarketItemFacade {
    @DubboReference
    private MarketItemProvider marketItemProvider;

    public Map<String, Long> queryIdBySapSkuCode (Set<String> set) {
        DubboResponse<Map<String, Long>> dubboResponse = marketItemProvider.queryIdBySapSkuCode (set);
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return dubboResponse.getData();
    }

    /**
     * 查询sku关联的商品
     *
     * @returnF
     */
    public Map<Long, List<MarketItemInfoResp>> queryAssociatedItemBySkuIds(Set<Long> skuIds, Long tenantId) {
        DubboResponse<Map<Long, List<MarketItemInfoResp>>> dubboResponse = marketItemProvider.queryAssociatedItemBySkuIds(skuIds, tenantId);
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return dubboResponse.getData();
    }

    public BatchChangNoGoodsSupplyPriceResultVO batchChangNoGoodsSupplyPrice(Long tenantId, List<ItemNoGoodsSupplyPriceExcelDataInput> input) {
        if (CollectionUtil.isEmpty(input)) {
            return new BatchChangNoGoodsSupplyPriceResultVO();
        }
        DubboResponse<BatchChangNoGoodsSupplyPriceResp> dubboResponse = marketItemProvider.batchChangNoGoodsSupplyPrice(tenantId, MarketFacadeConvert.INSTANCE.convert2ReqList(input));
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return MarketFacadeConvert.INSTANCE.convert2ResultVO(dubboResponse.getData());
    }

    public void updateItemCode(String itemCode, Long marketItemId, Long tenantId) {
        DubboResponse<Boolean> dubboResponse = marketItemProvider.updateItemCode(itemCode, marketItemId, tenantId);
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        if (Boolean.FALSE.equals(dubboResponse.getData())) {
            throw new ProviderException("未更新成功！");
        }
    }

    /**
     * 查询上架中的商品数量
     *
     * @param tenantId
     * @return
     */
    public Integer countOnsaleItem(Long tenantId) {
        if (ObjectUtil.isEmpty(tenantId)) {
            return 0;
        }
        DubboResponse<Integer> dubboResponse = marketItemProvider.countOnsaleItem(tenantId);
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return dubboResponse.getData();
    }

    /**
     * 分页查询商品项
     * 可控返回数据，
     * @param queryReq
     * @return
     */
    public PageInfo<MarketItemInfoPageVO> pageMarketItem(MarketItemCommonQueryReq queryReq) {
        DubboResponse<PageResp<MarketItemInfoResp>> dubboResponse = marketItemProvider.queryMarketItemList (queryReq);
        PageResp<MarketItemInfoResp> handler = RpcResponseUtil.handler(dubboResponse);
        PageInfo<MarketItemInfoPageVO> info = MarketConvert.INSTANCE.respToItemPageVO (handler);
        info.getList ().stream().filter (e->ObjectUtil.isNotNull (e.getStandardUnitPrice ()) && ObjectUtil.isNotNull (e.getStoreInventoryCostUnitMultiple ())).forEach (e->{
            e.setCostUnitPrice (e.getStandardUnitPrice ().divide (e.getStoreInventoryCostUnitMultiple (), NumberConstants.TWO, BigDecimal.ROUND_HALF_UP));
        });
        return info;
    }

    /**
     * 分页查询商品项   商品信息较全
     * @param queryDTO
     * @return
     */
    public PageInfo<MarketItemInfoDTO> pageMarketItemAllInfo(MarketItemQueryDTO queryDTO) {
        MarketItemCommonQueryReq marketItemCommonQueryReq = MarketFacadeConvert.INSTANCE.convert2Query (queryDTO);
        MarketItemInfoQueryFlagReq marketItemInfoQueryFlagReq = new MarketItemInfoQueryFlagReq();
        marketItemInfoQueryFlagReq.setPriceRangeFlag(true);
        marketItemInfoQueryFlagReq.setStockFlag(true);
        marketItemInfoQueryFlagReq.setCategoryIdFlag(true);
        marketItemInfoQueryFlagReq.setClassificationIdFlag (false);
        marketItemInfoQueryFlagReq.setUnitFlag (true);
        marketItemCommonQueryReq.setMarketItemInfoQueryFlagReq (marketItemInfoQueryFlagReq);

        DubboResponse<PageResp<MarketItemInfoResp>> dubboResponse = marketItemProvider.queryMarketItemList(marketItemCommonQueryReq);
        FacadeExceptionUtil.executeFaceException(dubboResponse, null);
        return MarketFacadeConvert.INSTANCE.convert2ItemDtoPage(dubboResponse.getData());
    }
    /**
     * 查询marketItemMap
     *
     * @param queryDTO
     * @return
     */
    public Map<Long, MarketItemInfoDTO> queryMarketItemMap(MarketItemQueryDTO queryDTO) {
        PageInfo<MarketItemInfoDTO> pageInfo = pageMarketItemAllInfo(queryDTO);
        List<MarketItemInfoDTO> marketItemList = pageInfo.getList();
        return marketItemList.stream().collect(Collectors.toMap(MarketItemInfoDTO::getItemId, Function.identity(), (k1, k2) -> k2));
    }
}
