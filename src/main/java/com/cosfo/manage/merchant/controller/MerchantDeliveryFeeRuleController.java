package com.cosfo.manage.merchant.controller;

import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.facade.usercenter.UserCenterTenantFacade;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleDTO;
import com.cosfo.manage.tenant.model.dto.TenantDeliveryFeeRuleDTO;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import net.xianmu.marketing.center.client.freight.provider.DeliveryFeeRuleQueryProvider;
import net.xianmu.marketing.center.client.freight.provider.DistributionRulesProvider;
import net.xianmu.marketing.center.client.freight.req.DistributionRulesInfoByAddressReq;
import net.xianmu.marketing.center.client.freight.resp.DistributionRulesInfoByAddressResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/27 15:08
 */
@RestController
@RequestMapping("/merchantDeliveryFeeRule")
public class MerchantDeliveryFeeRuleController extends BaseController {

    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;

    @DubboReference
    private DistributionRulesProvider distributionRulesProvider;

    @DubboReference
    private DeliveryFeeRuleQueryProvider deliveryFeeRuleQueryProvider;

    /**
     * 运费配置列表
     *
     * @return
     */
    @RequestMapping(value = "/listAllRule", method = RequestMethod.GET)
    public ResultDTO<List<MerchantDeliveryFeeRuleDTO>> listAll() {
        LoginContextInfoDTO loginInfoDTO = getMerchantInfoDTO();
        Long tenantId = loginInfoDTO.getTenantId();
        return ResultDTO.success();
    }

    /**
     * 更新运费规则
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:order-delivery-fee-rule:update", expireError = true)
    @RequestMapping(value = "/updateDeliveryFeeRule", method = RequestMethod.PUT)
    public ResultDTO updateDeliveryFeeRule(@RequestBody List<MerchantDeliveryFeeRuleDTO> ruleDTOList) {
        return ResultDTO.success();
    }

    /**
     * 查询品牌方运费规则
     *
     * @return
     */
    @PostMapping("/query/tenant/delivery/rule")
    public ResultDTO<TenantDeliveryFeeRuleDTO> queryTenantDeliveryRule() {
        return ResultDTO.success();
    }


    /**
     * 根据区域查询鲜沐运费规则，包含日配、非日配
     *
     * @param req
     * @return
     */
    @PostMapping("/query/summerfarm/delivery/rule/v2")
    @Deprecated
    public CommonResult<DistributionRulesInfoByAddressResp> querySummerfarmDeliveryFeeRuleV2(@RequestBody @Valid DistributionRulesInfoByAddressReq req) {
        DistributionRulesInfoByAddressResp resp = RpcResponseUtil.handler(distributionRulesProvider.getInfoByAddress(req));
        return CommonResult.ok(resp);
    }
}
