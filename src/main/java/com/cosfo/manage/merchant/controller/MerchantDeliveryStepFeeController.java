package com.cosfo.manage.merchant.controller;


import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.merchant.model.dto.DeliveryFeeRuleEditWrapperDTO;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryInitStepDTO;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleVO;
import com.cosfo.manage.merchant.service.MerchantDeliveryFeeRuleService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 阶梯运费
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@RestController
@RequestMapping("/delivery-fee-rule")
public class MerchantDeliveryStepFeeController {

    @Resource
    private MerchantDeliveryFeeRuleService merchantDeliveryFeeRuleService;


    /**
     * 运费配置列表
     * @return
     */
    @PostMapping(value = "/query/list")
    public CommonResult<List<MerchantDeliveryFeeRuleVO>> listAll() {
        Long tenantId = UserLoginContextUtils.getTenantId();
        return CommonResult.ok(merchantDeliveryFeeRuleService.listRule(tenantId));
    }

    /**
     * 更新运费规则
     * @param deliveryFeeRule
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/update")
    public CommonResult<Boolean> updateDeliveryFeeRule(@RequestBody DeliveryFeeRuleEditWrapperDTO deliveryFeeRule) {

        return CommonResult.ok(merchantDeliveryFeeRuleService.updateDeliveryStepFeeRule(deliveryFeeRule));
    }


    /**
     * 历史数据迁移
     * @param dto
     * @retur
     */
    @PostMapping("/init/step")
    public CommonResult<Void> initStepFee(@RequestBody MerchantDeliveryInitStepDTO dto){
        return CommonResult.ok();
    }
}

