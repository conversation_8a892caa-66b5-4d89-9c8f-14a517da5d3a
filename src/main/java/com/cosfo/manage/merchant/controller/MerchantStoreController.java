package com.cosfo.manage.merchant.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.dto.UpdateStoreInfoReqDTO;
import com.cosfo.manage.merchant.model.vo.MerchantStoreVO;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import net.xianmu.log.annation.BizLogRecord;
import net.xianmu.log.config.BizLogRecordContext;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @description 门店
 * <AUTHOR>
 * @date 2022/5/21 11:34
 */
@RestController
@RequestMapping("/merchant/store")
public class MerchantStoreController extends BaseController {

    @Resource
    private MerchantStoreService merchantStoreService;

    /**
     * 批量注册门店信息
     *
     * @param file
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/export/batchStoreRegister")
    public CommonResult batchStoreRegister(@RequestBody MultipartFile file, HttpServletResponse response) throws IOException {
        return CommonResult.ok();
    }

    /**
     * 导入三方门店映射
     *
     * @param file
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/importThirdMap")
    public CommonResult<ExcelImportResDTO> importThirdMap(@RequestBody MultipartFile file, HttpServletResponse response) throws IOException {

        return CommonResult.ok(merchantStoreService.saveStoreCodeMapping(file, getMerchantInfoDTO().getTenantId()));
    }

    /**
     * 导出三方门店映射模版
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/query/exportThirdMapTemplate")
    public CommonResult<String> exportThirdMapTemplate() {
        return CommonResult.ok(merchantStoreService.exportThirdMapTemplate());
    }

    /**
     * 新建门店
     *
     * @param merchantStoreVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:customer-manage:add", expireError = true)
    @PostMapping(value = "/save")
    public ResultDTO save(@RequestBody MerchantStoreVO merchantStoreVO) {
        MerchantStoreDTO merchantStoreDTO = new MerchantStoreDTO();
        BeanUtils.copyProperties(merchantStoreVO, merchantStoreDTO);
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return merchantStoreService.saveStore(merchantStoreDTO, loginContextInfoDTO);
    }

    /**
     * desc: 修改运营状态
     *
     * @param merchantStoreVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PutMapping(value = "/updateOperateStatus")
    @BizLogRecord(operationName = "变更门店信息", bizKey = "#storeId", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public ResultDTO updateOperateStatus(@RequestBody MerchantStoreVO merchantStoreVO) {
        MerchantStoreDTO merchantStoreDTO = new MerchantStoreDTO();
        BeanUtils.copyProperties(merchantStoreVO, merchantStoreDTO);
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        BizLogRecordContext.put("tenantId",loginContextInfoDTO.getTenantId());
        return merchantStoreService.updateOperateStatus(merchantStoreDTO, loginContextInfoDTO);
    }

    /**
     * desc: 门店导入模板下载
     *
     * @return
     */
//    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @GetMapping(value = "/export-template")
    public ResultDTO exportTemplate() {
        return merchantStoreService.exportTemplate();
    }

    /**
     * desc: 门店导出
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/export")
    public CommonResult export(@RequestBody MerchantStoreVO storeVO) {
        MerchantStoreQueryDTO merchantStoreQueryDTO = new MerchantStoreQueryDTO();
        BeanUtils.copyProperties(storeVO, merchantStoreQueryDTO);
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return merchantStoreService.export(merchantStoreQueryDTO, loginContextInfoDTO);
    }

    @PostMapping("/query/all-store-city")
    public CommonResult<List<String>> queryAllStoreCity(){
        List<String> cityNames = merchantStoreService.queryAllStoreCity(getMerchantInfoDTO());
        return CommonResult.ok(cityNames);
    }


    /**
     * 门店列表
     * @param storeVO
     * @return
     */
    @RequestMapping(value = "/listAll", method = RequestMethod.POST)
    public CommonResult<PageInfo<MerchantStoreDTO>> listAll(@RequestBody MerchantStoreVO storeVO) {
        MerchantStoreQueryDTO merchantStoreQueryDTO = new MerchantStoreQueryDTO();
        BeanUtils.copyProperties(storeVO, merchantStoreQueryDTO);
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(merchantStoreService.listAll(merchantStoreQueryDTO, contextInfoDTO));
    }

    /**
     * 门店详情
     * @param id
     * @return
     */
    @RequiresPermissions(value = {"cosfo_manage:customer-manage:query"}, logical = Logical.OR)
    @RequestMapping(value = "/detail/{id}", method = RequestMethod.GET)
    public CommonResult<MerchantStoreDTO> selectDetail(@PathVariable Long id) {
        return CommonResult.ok(merchantStoreService.selectDetail(id));
    }
    /**
     * 更新门店
     * @param storeVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:customer-manage-detail:update", expireError = true)
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @BizLogRecord(operationName = "变更门店信息", bizKey = "#storeId", bizKeyTenantId = "#tenantId", content = "T(com.alibaba.fastjson.JSONObject).toJSONString(#content)")
    public ResultDTO updateStore(@RequestBody MerchantStoreVO storeVO) {
        MerchantStoreDTO storeDTO = new MerchantStoreDTO();
        BeanUtils.copyProperties(storeVO, storeDTO);
        return merchantStoreService.updateStore(storeDTO,getMerchantInfoDTO());
    }


    /**
     * 更新门店信息后门
     * @param updateStoreInfoReqDTO
     * @return
     */
    @RequestMapping(value = "/updateStoreInfo", method = RequestMethod.POST)
    public CommonResult<Void> updateStoreInfo(@RequestBody UpdateStoreInfoReqDTO updateStoreInfoReqDTO) {
        merchantStoreService.updateStoreInfo(updateStoreInfoReqDTO);
        return CommonResult.ok();
    }

}
