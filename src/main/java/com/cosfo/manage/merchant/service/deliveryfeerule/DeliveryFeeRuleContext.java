package com.cosfo.manage.merchant.service.deliveryfeerule;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DeliveryFeeRuleContext {
    @Resource
    private List<DeliveryFeeRuleStrategy> deliveryFeeRuleStrategies;

    public DeliveryFeeRuleStrategy process(Integer type) {
        for (DeliveryFeeRuleStrategy deliveryFeeRuleStrategy : deliveryFeeRuleStrategies) {
            if (deliveryFeeRuleStrategy.type().equals(type)) {
                return deliveryFeeRuleStrategy;
            }
        }
        throw new BizException("規則配置異常");
    }
}
