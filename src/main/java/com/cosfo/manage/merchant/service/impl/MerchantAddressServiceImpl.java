package com.cosfo.manage.merchant.service.impl;

import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.DefaultFlagEnum;
import com.cosfo.manage.common.context.usercenter.MerchantAddressStatusEnum;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantAddressFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantContactFacade;
import com.cosfo.manage.merchant.convert.MerchantAddressMapperConvert;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryAddressResultDTO;
import com.cosfo.manage.merchant.service.MerchantAddressService;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 门店地址服务层
 * @author: George
 * @date: 2023-05-14
 **/
@Service
public class MerchantAddressServiceImpl implements MerchantAddressService {

//    @Resource
//    private MerchantAddressMapper merchantAddressMapper;

    @Resource
    private UserCenterMerchantAddressFacade userCenterMerchantAddressFacade;
    @Resource
    private UserCenterMerchantContactFacade userCenterMerchantContactFacade;

    @Override
    public List<MerchantDeliveryAddressResultDTO> selectByStoreIds(Long tenantId, List<Long> storeIds) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(tenantId);
        req.setStoreIdList(storeIds);
        req.setDefaultFlag(DefaultFlagEnum.TURE.getFlag());
        req.setStatus(MerchantAddressStatusEnum.NORMAL.getStatus());
        List<MerchantAddressResultResp> merchantAddressList = userCenterMerchantAddressFacade.getMerchantAddressList(req);
        if (CollectionUtils.isEmpty(merchantAddressList)) {
            return Collections.emptyList();
        }

        List<MerchantDeliveryAddressResultDTO> list = MerchantAddressMapperConvert.INSTANCE.respListToDtoList(merchantAddressList);
        List<Long> addressIdList = list.stream().map(MerchantDeliveryAddressResultDTO::getId).collect(Collectors.toList());

        MerchantContactQueryReq merchantContactQueryReq = new MerchantContactQueryReq();
        merchantContactQueryReq.setAddressIdList(addressIdList);
        merchantContactQueryReq.setDefaultFlag(DefaultFlagEnum.TURE.getFlag());
        merchantContactQueryReq.setTenantId(tenantId);
        List<MerchantContactResultResp> merchantContacts = userCenterMerchantContactFacade.getMerchantContacts(merchantContactQueryReq);
        Map<Long, MerchantContactResultResp> contactResultRespMap = merchantContacts.stream().collect(Collectors.toMap(MerchantContactResultResp::getAddressId, Function.identity(), (v1, v2) -> v1));

        for (MerchantDeliveryAddressResultDTO dto : list) {
            dto.setDeliveryAddress(StringUtils.join(dto.getProvince(), dto.getCity(), dto.getArea(), dto.getAddress(), Optional.ofNullable(dto.getHouseNumber()).orElse(StringUtils.EMPTY)));
            MerchantContactResultResp merchantContactResultResp = contactResultRespMap.get(dto.getId());
            if (Objects.isNull(merchantContactResultResp)) {
                continue;
            }
            dto.setContactPhone(merchantContactResultResp.getPhone());
            dto.setContactName(merchantContactResultResp.getName());
        }
        return list;
    }

    @Override
    public List<MerchantAddressResultResp> selectAddressListByStoreIds(Long tenantId, List<Long> storeIds) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(tenantId);
        req.setStoreIdList(storeIds);
        req.setDefaultFlag(DefaultFlagEnum.TURE.getFlag());
        req.setStatus(MerchantAddressStatusEnum.NORMAL.getStatus());
        List<MerchantAddressResultResp> merchantAddressList = userCenterMerchantAddressFacade.getMerchantAddressList(req);
        if (CollectionUtils.isEmpty(merchantAddressList)) {
            return Collections.emptyList();
        }
        return merchantAddressList;
    }

    @Override
    public MerchantAddressResultResp selectByStoreId(Long tenantId, Long storeId) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(tenantId);
        req.setStoreId(storeId);
        req.setDefaultFlag(DefaultFlagEnum.TURE.getFlag());
        req.setStatus(MerchantAddressStatusEnum.NORMAL.getStatus());
        List<MerchantAddressResultResp> merchantAddressList = userCenterMerchantAddressFacade.getMerchantAddressList(req);
        if (CollectionUtils.isEmpty(merchantAddressList)) {
            return null;
        }
        return merchantAddressList.get(NumberConstant.ZERO);
    }

    @Override
    public List<MerchantAddressResultResp> selectAddressListByStoreId(Long tenantId, Long storeId) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(tenantId);
        req.setStoreId(storeId);
        req.setStatus(MerchantAddressStatusEnum.NORMAL.getStatus());
        List<MerchantAddressResultResp> merchantAddressList = userCenterMerchantAddressFacade.getMerchantAddressList(req);
        if (CollectionUtils.isEmpty(merchantAddressList)) {
            return Collections.emptyList();
        }
        return merchantAddressList;
    }

    @Override
    public List<MerchantAddressResultResp> queryStore(String province, String city, String area, Long tenantId) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setProvince(province);
        req.setCity(city);
        req.setArea(area);
        req.setTenantId(tenantId);
        List<MerchantAddressResultResp> merchantAddressList = userCenterMerchantAddressFacade.getMerchantAddressList(req);
        if(Objects.isNull(merchantAddressList)){
            return Collections.emptyList();
        }
        return merchantAddressList;
    }

    @Override
    public List<MerchantAddressResultResp> queryByCityList(List<String> cityList, Long tenantId) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setCityList(cityList);
        req.setTenantId(tenantId);
        List<MerchantAddressResultResp> merchantAddressList = userCenterMerchantAddressFacade.getMerchantAddressList(req);
        if (Objects.isNull(merchantAddressList)) {
            return Collections.emptyList();
        }
        return merchantAddressList;
    }

    @Override
    public List<String> queryAllStoreCity(Long tenantId) {
        List<MerchantAddressResultResp> merchantAddressList = selectRespByTenantId(tenantId);
        return merchantAddressList.stream().map(MerchantAddressResultResp::getCity).distinct().collect(Collectors.toList());
    }

    private List<MerchantAddressResultResp> selectRespByTenantId(Long tenantId) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(tenantId);
        List<MerchantAddressResultResp> merchantAddressList = userCenterMerchantAddressFacade.getMerchantAddressList(req);
        if (Objects.isNull(merchantAddressList)) {
            return Collections.emptyList();
        }
        return merchantAddressList;
    }

    @Override
    public List<String> selectByTenantId(Long tenantId) {
        List<MerchantAddressResultResp> merchantAddressList = selectRespByTenantId(tenantId);
        return merchantAddressList.stream().map(resp -> StringUtils.join(resp.getProvince(), StringConstants.SEPARATING_IN_LINE, resp.getCity(), StringConstants.SEPARATING_IN_LINE, resp.getArea()))
                .distinct().collect(Collectors.toList());
    }

    @Override
    public List<MerchantAddressResultResp> listStoreIdAndAddress(Long tenantId) {
        return selectRespByTenantId(tenantId);
    }


    public boolean deleteById(Long id) {
        MerchantAddressCommandReq req = new MerchantAddressCommandReq();
        req.setId(id);
        Boolean remove = userCenterMerchantAddressFacade.remove(req);
        return Boolean.TRUE.equals(remove);
    }
}
