package com.cosfo.manage.merchant.service;

import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAddressDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.model.dto.UpdateStoreInfoReqDTO;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import com.cosfo.manage.merchant.model.vo.MerchantStoreVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/23 10:27
 */
public interface MerchantStoreService {

    /** 门店列表
     *
     * @param merchantStoreQueryDTO
     * @param contextInfoDTO
     * @return
     */
    PageInfo<MerchantStoreDTO> listAll(MerchantStoreQueryDTO merchantStoreQueryDTO, LoginContextInfoDTO contextInfoDTO);
    List<MerchantStore> listStore(MerchantStoreQueryDTO storeQueryDTO, LoginContextInfoDTO contextInfoDTO);

    /**
     * 门店详情
     * @param id
     * @return
     */
    MerchantStoreDTO selectDetail(Long id);


    /**
     * 更新门店
     * @param storeDTO
     * @return
     */
    ResultDTO updateStore(MerchantStoreDTO storeDTO,LoginContextInfoDTO contextInfoDTO);

    /**
     * 订单列表查询商户门店信息
     *
     * @param orderQueryDTO
     * @return
     */
    List<MerchantStoreResultResp> queryMerchantStore(Integer storeType, String storeName, String phone, Long tenantId);


    /**
     * 查询门店信息
     *
     * @param storeId
     * @return
     */
    MerchantStoreDTO queryStore(Long storeId,Long tenantId);

    /**
     * 等待门店审核数量
     *
     * @param loginContextInfoDTO
     * @return
     */
    Integer waitAuditStoreNum(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 下单有效期临期门店数量
     *
     * @param loginContextInfoDTO
     * @return
     */
    Integer placeOrderDeadlineNum(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 批量查询门店信息
     *
     * @param storeIds
     * @param tenantId
     * @return
     */
    List<MerchantStoreDTO> batchQuery(List<Long> storeIds, Long tenantId);

    /**
     * 批量查询门店信息
     *
     * @param storeIds
     * @param tenantId
     * @return
     */
    List<MerchantStoreDTO> batchQueryByStoreIds(List<Long> storeIds, Long tenantId);

    /**
     * 批量查询门店详细信息
     *
     * @param storeIds
     * @param tenantId
     * @return
     */
    List<MerchantStoreDTO> batchQueryDetailByStoreIds(List<Long> storeIds, Long tenantId);

    /**
     * desc: 新建门店
     *
     * @param merchantStoreDTO
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO saveStore(MerchantStoreDTO merchantStoreDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * desc: 更新门店的运营状态
     *
     * @param merchantStoreDTO
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO updateOperateStatus(MerchantStoreDTO merchantStoreDTO, LoginContextInfoDTO loginContextInfoDTO);


    /**
     * 返回门店导入模板路径
     * @return
     */
    ResultDTO exportTemplate();

    /**
     * 导出门店
     * @param merchantStoreQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult export(MerchantStoreQueryDTO merchantStoreQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

//    /**
//     * 生成门店导出文件
//     * @param merchantStoreQueryDTO
//     * @param fileDownloadRecord
//     */
//    void generateStoreExportFile(MerchantStoreQueryDTO merchantStoreQueryDTO, Long fileDownloadRecord);

    /**
     * 查询所有门店城市
     *
     * @param loginContextInfoDTO
     * @return
     */
    List<String> queryAllStoreCity(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询列表
     * @param storeQueryDTO
     * @return
     */
    List<MerchantStoreDTO> listByCondition(MerchantStoreQueryDTO storeQueryDTO);
    /**
     * 查询列表
     * @param storeQueryDTO
     * @return
     */
    List<MerchantStoreDTO> listByConditionNew(MerchantStoreQueryDTO storeQueryDTO);

    List<MerchantStoreVO> listMerchantStoreVO(List<Long> storeIds, Long tenantId);

    /**
     * 查询符合条件的门店ID列表
     * @param tenantId
     * @param storeNo
     * @param storeName
     * @param storeType
     * @param storeGroupId
     * @return
     */
    List<Long> selectIdListByParam(Long tenantId, String storeNo, String storeName, Integer storeType, Long storeGroupId);

    List<String> listAddress(Long tenantId);

     List<MerchantStoreAddressDTO> listStoreIdAndAddress(Long tenantId,List<Long> storeIds);

    MerchantStoreAddressDTO getStoreIdAndAddress(Long tenantId,Long storeId);

    /**
     * 根据租户id批量查询
     * @param tenantId
     * @return
     */
    List<MerchantStore> selectByTenantId(Long tenantId);

    /**
     *
     * @param tenantId
     * @param storeName
     * @return
     */
    MerchantStore selectByStoreName(Long tenantId, String storeName);

    /**
     * 模糊查询
     * @param tenantId
     * @param storeName
     * @return
     */
    List<MerchantStore> selectByStoreNameLike(Long tenantId, String storeName);

    /**
     * 分页查询
     * @param storeQueryDTO
     * @return
     */
    PageInfo<MerchantStoreDTO> pageList(MerchantStoreQueryDTO storeQueryDTO);

    /**
     * 分页查询
     * @param storeQueryDTO
     * @return
     */
    List<MerchantStoreDTO> listAll(MerchantStoreQueryDTO storeQueryDTO);

    /**
     *
     * @param tenantId
     * @param storeIds
     * @return
     */
    List<MerchantStoreDTO> selectList(Long tenantId, List<Long> storeIds);

    /**
     * 查询租户下的门店数量
     * @param tenantId
     * @return
     */
    Integer selectCountByTenantId(Long tenantId);


    /**
     * 保存门店三方映射关系
     * @param file
     * @param tenantId
     */
    ExcelImportResDTO saveStoreCodeMapping(MultipartFile file, Long tenantId);

    String exportThirdMapTemplate();

    void updateStoreInfo(UpdateStoreInfoReqDTO storeDTO);
}
