package com.cosfo.manage.merchant.service;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.merchant.model.dto.DeliveryFeeRuleEditWrapperDTO;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleDTO;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleVO;
import com.cosfo.manage.tenant.model.dto.TenantDeliveryFeeRuleDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/27 15:16
 */
public interface MerchantDeliveryFeeRuleService {

    /**
     * 查询门店下的租户规则
     * @param tenantId
     * @return
     */
    ResultDTO<List<MerchantDeliveryFeeRuleDTO>> listAll(Long tenantId);

    /**
     * 查询对品牌方运费规则
     *
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO<TenantDeliveryFeeRuleDTO> queryTenantDeliveryRule(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 更新运费配置规则
     * @param deliveryFeeRule
     * @return
     */
    Boolean updateDeliveryStepFeeRule(DeliveryFeeRuleEditWrapperDTO deliveryFeeRule);

    /**
     * 获取所有运费配置
     * @return
     */
    List<MerchantDeliveryFeeRuleVO> listRule(Long tenantId);


    /**
     * 历史运费规则初始化成阶梯运费
     * @param tenantIds
     * @return
     */
    void initDeliveryStepFeeRule(List<Long> tenantIds);


    /**
     * 更新运费配置,移除删除的item项
     * @return
     */
    void updateDeliveryFeeRuleWithItemDelete(Long tenantId, Long marketItemId);

}
