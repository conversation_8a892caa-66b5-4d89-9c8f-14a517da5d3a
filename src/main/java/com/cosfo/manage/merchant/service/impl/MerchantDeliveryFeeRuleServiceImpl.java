package com.cosfo.manage.merchant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.DefaultTypeEnum;
import com.cosfo.manage.common.context.MerchantDeliveryFeeRuleEnum;
import com.cosfo.manage.common.context.WarehouseTypeEnum;
import com.cosfo.manage.common.converter.MerchangeDeliveryFeeRuleMapper;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.merchant.convert.MerchantDeliveryFeeRuleConvert;
import com.cosfo.manage.merchant.mapper.MerchantDeliveryFeeRuleMapper;
import com.cosfo.manage.merchant.model.dto.DeliveryFeeRuleEditWrapperDTO;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleDTO;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleEditDTO;
import com.cosfo.manage.merchant.model.dto.balance.MerchantDeliveryFeeSubRuleDTO;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryRuleWarehouseRelation;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryStepFee;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleVO;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryStepFeeVO;
import com.cosfo.manage.merchant.repository.MerchantDeliveryStepFeeRepository;
import com.cosfo.manage.merchant.service.MerchantDeliveryFeeRuleService;
import com.cosfo.manage.merchant.service.MerchantDeliveryRuleWarehouseRelationService;
import com.cosfo.manage.merchant.service.deliveryfeerule.DeliveryFeeRuleContext;
import com.cosfo.manage.tenant.model.dto.TenantDeliveryFeeRuleDTO;
import com.cosfo.manage.tenant.service.TenantDeliveryFeeRuleService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/27 15:21
 */
@Slf4j
@Service
public class MerchantDeliveryFeeRuleServiceImpl implements MerchantDeliveryFeeRuleService {

    @Resource
    private MerchantDeliveryFeeRuleMapper merchantDeliveryFeeRuleMapper;
    @Resource
    private TenantDeliveryFeeRuleService tenantDeliveryFeeRuleService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private MerchantDeliveryRuleWarehouseRelationService merchantDeliveryRuleWarehouseRelationService;
    @Resource
    private MerchantDeliveryStepFeeRepository merchantDeliveryStepFeeRepository;
    @Resource
    private DeliveryFeeRuleContext deliveryFeeRuleContext;

    @Override
    public ResultDTO<List<MerchantDeliveryFeeRuleDTO>> listAll(Long tenantId) {
        List<MerchantDeliveryFeeRule> ruleList = merchantDeliveryFeeRuleMapper.listAll(tenantId);
        List<MerchantDeliveryFeeRuleDTO> ruleDTOList = ruleList.stream().map(rule -> {
            MerchantDeliveryFeeRuleDTO merchantDeliveryFeeRuleDTO = new MerchantDeliveryFeeRuleDTO();
            BeanUtils.copyProperties(rule, merchantDeliveryFeeRuleDTO);
            return merchantDeliveryFeeRuleDTO;
        }).collect(Collectors.toList());

        List<MerchantDeliveryFeeRuleDTO> defaultRuleList = ruleDTOList.stream().filter(rule -> DefaultTypeEnum.DEFAULT.getCode().equals(rule.getDefaultType())).collect(Collectors.toList());
        List<MerchantDeliveryFeeSubRuleDTO> noDefaultRuleList = ruleDTOList.stream().filter(rule -> DefaultTypeEnum.NO_DEFAULT.getCode().equals(rule.getDefaultType())).map(MerchangeDeliveryFeeRuleMapper.INSTANCE::ruleDtoToSubDto).collect(Collectors.toList());
        // 非默认列表
        List<Long> ruleIdList = noDefaultRuleList.stream().map(MerchantDeliveryFeeSubRuleDTO::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ruleIdList)) {
            return ResultDTO.success(ruleDTOList);
        }

        // 组装关联数据
        List<MerchantDeliveryRuleWarehouseRelation> relations = merchantDeliveryRuleWarehouseRelationService.queryByRuleIds(tenantId, ruleIdList);
        Map<Long, List<MerchantDeliveryRuleWarehouseRelation>> ruleIdMap = relations.stream().collect(Collectors.groupingBy(MerchantDeliveryRuleWarehouseRelation::getRuleId));
        Map<Integer, String> warehouseStorageMap = Collections.EMPTY_MAP;
        for (MerchantDeliveryFeeSubRuleDTO merchantDeliveryFeeRuleDTO : noDefaultRuleList) {
            List<MerchantDeliveryRuleWarehouseRelation> list = ruleIdMap.get(merchantDeliveryFeeRuleDTO.getId());
            if (CollectionUtil.isEmpty(list)) {
                continue;
            }
            List<Integer> warehouseNoList = list.stream().sorted(Comparator.comparing(MerchantDeliveryRuleWarehouseRelation::getWarehouseNo)).map(MerchantDeliveryRuleWarehouseRelation::getWarehouseNo).collect(Collectors.toList());

            merchantDeliveryFeeRuleDTO.setWarehouseNoList(warehouseNoList);
            List<String> warehouseNameList = getWarehouseNameList(warehouseStorageMap, warehouseNoList);
            merchantDeliveryFeeRuleDTO.setWarehouseNameList(warehouseNameList);
        }

        defaultRuleList.stream()
                .filter(dto -> WarehouseTypeEnum.PROPRIETARY.getCode().equals(dto.getType()))
                .findFirst()
                .ifPresent(merchantDeliveryFeeRuleDTO -> merchantDeliveryFeeRuleDTO.setMerchantDeliveryFeeSubRuleDTOList(noDefaultRuleList));
        return ResultDTO.success(defaultRuleList);
    }

    private List<String> getWarehouseNameList(Map<Integer, String> warehouseStorageMap, List<Integer> warehouseNo) {
        if (CollectionUtil.isEmpty(warehouseNo)) {
            return Lists.newArrayList();
        }
        return warehouseNo.stream().filter(no -> Objects.nonNull(warehouseStorageMap.get(no))).map(warehouseStorageMap::get).collect(Collectors.toList());
    }

    /**
     * 校验
     *
     * @param dbList
     * @param ruleDTOList
     * @return
     */
    private void validWarehouseNo(List<MerchantDeliveryFeeRule> dbList, List<MerchantDeliveryFeeRuleDTO> ruleDTOList) {
        List<Long> dbDefaultIds = dbList.stream().filter(dto -> DefaultTypeEnum.DEFAULT.getCode().equals(dto.getDefaultType())).map(MerchantDeliveryFeeRule::getId).collect(Collectors.toList());
        List<Long> newIds = ruleDTOList.stream().filter(dto -> Objects.nonNull(dto.getId())).map(MerchantDeliveryFeeRuleDTO::getId).collect(Collectors.toList());
        AssertCheckParams.expectTrue(newIds.containsAll(dbDefaultIds), ResultStatusEnum.SERVER_ERROR.getStatus(), "不可移除默认数据");


        // 手动添加的仓库运费，必须要有仓库编号
        MerchantDeliveryFeeRuleDTO merchantDeliveryFeeRuleDTO = ruleDTOList.stream()
                .filter(dto -> WarehouseTypeEnum.PROPRIETARY.getCode().equals(dto.getType()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(merchantDeliveryFeeRuleDTO)) {
            return;
        }

        List<MerchantDeliveryFeeSubRuleDTO> changeList = Optional.ofNullable(merchantDeliveryFeeRuleDTO.getMerchantDeliveryFeeSubRuleDTOList()).orElse(Lists.newArrayList());
        boolean existNull = changeList.stream().anyMatch(dto -> CollectionUtil.isEmpty(dto.getWarehouseNoList()));
        AssertCheckParams.expectTrue(!existNull, ResultStatusEnum.SERVER_ERROR.getStatus(), "仓库编号不能为空");


        List<List<Integer>> list = changeList.stream().map(MerchantDeliveryFeeSubRuleDTO::getWarehouseNoList).collect(Collectors.toList());
        int size = NumberConstants.ZERO;
        Set<Integer> totalSet = Sets.newHashSet();
        for (List<Integer> warehouseNoList : list) {
            totalSet.addAll(warehouseNoList);
            size = size + warehouseNoList.size();
        }
        AssertCheckParams.expectTrue(totalSet.size() == size, ResultStatusEnum.SERVER_ERROR.getStatus(), "仓库编号不可重复");
    }

    private ResultDTO executeUpdateDeliveryFeeRule(List<MerchantDeliveryFeeRuleDTO> ruleDTOList, Long tenantId, List<Long> deleteIds, List<MerchantDeliveryFeeSubRuleDTO> noDefaultList) {
        // 如果自营仓规则有id减少，删除数据
        if (CollectionUtil.isNotEmpty(deleteIds)) {
            merchantDeliveryFeeRuleMapper.deleteByIds(deleteIds, tenantId);
            merchantDeliveryRuleWarehouseRelationService.deleteByRuleIds(deleteIds, tenantId);
        }
        // 如果存在,创建新运费规则
        List<MerchantDeliveryFeeSubRuleDTO> addRuleDTOList = noDefaultList.stream().filter(merchantDeliveryFeeRuleDTO -> Objects.isNull(merchantDeliveryFeeRuleDTO.getId())).collect(Collectors.toList());
        ResultDTOEnum resultDTOEnum = createNoDefaultRuleList(addRuleDTOList, tenantId);
        if (Objects.nonNull(resultDTOEnum)) {
            return ResultDTO.fail(resultDTOEnum);
        }

        // 获取自营仓配置的仓库编号关联数据
        List<MerchantDeliveryRuleWarehouseRelation> list = merchantDeliveryRuleWarehouseRelationService.queryByTenantId(Collections.singletonList(tenantId));
        Map<Long, List<MerchantDeliveryRuleWarehouseRelation>> ruleIdMap = list.stream().collect(Collectors.groupingBy(MerchantDeliveryRuleWarehouseRelation::getRuleId));

        // 已存在id的走更新逻辑
        List<MerchantDeliveryFeeSubRuleDTO> existList = noDefaultList.stream().filter(merchantDeliveryFeeRuleDTO -> Objects.nonNull(merchantDeliveryFeeRuleDTO.getId())).collect(Collectors.toList());
        List<MerchantDeliveryFeeSubRuleDTO> defaultList = ruleDTOList.stream().map(dto -> MerchangeDeliveryFeeRuleMapper.INSTANCE.ruleDtoToSubDto(dto)).collect(Collectors.toList());
        existList.addAll(defaultList);
        for (MerchantDeliveryFeeSubRuleDTO ruleDTO : existList) {
            MerchantDeliveryFeeRule rule = new MerchantDeliveryFeeRule();
            if (!MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.FOLLOW_WAREHOUSE.getCode().equals(ruleDTO.getRuleType())) {
                // 每单、每日
                BigDecimal deliveryFee = ruleDTO.getDeliveryFee();
                if (deliveryFee.compareTo(BigDecimal.ZERO) < NumberConstants.ZERO || deliveryFee.compareTo(NumberConstants.FOUR_NINE) > NumberConstants.ZERO) {
                    return ResultDTO.fail(ResultDTOEnum.DELIVERY_FEE_ERROR);
                }
                if (deliveryFee.scale() > NumberConstants.TWO) {
                    return ResultDTO.fail(ResultDTOEnum.DELIVERY_FEE_SCALE_ERROR);
                }
            } else {
                AssertCheckParams.notNull(ruleDTO.getRelateNumber(), ResultDTOEnum.FOLLOW_WAREHOUSE_FAILED.getCode(), ResultDTOEnum.FOLLOW_WAREHOUSE_FAILED.getMessage());
                // 基于仓运费报价
            }
            BeanUtils.copyProperties(ruleDTO, rule);
            merchantDeliveryFeeRuleMapper.updateByPrimaryKeySelective(rule);
            if (DefaultTypeEnum.DEFAULT.getCode().equals(rule.getDefaultType())) {
                continue;
            }
            // 处理仓库编号关联数据
            List<MerchantDeliveryRuleWarehouseRelation> relations = ruleIdMap.get(rule.getId());
            dealRelationData(relations, ruleDTO, tenantId);
        }
        return ResultDTO.success();
    }

    private ResultDTOEnum createNoDefaultRuleList(List<MerchantDeliveryFeeSubRuleDTO> addRuleDTOList, Long tenantId) {
        for (MerchantDeliveryFeeSubRuleDTO ruleDTO : addRuleDTOList) {
            // 每单、每日
            BigDecimal deliveryFee = ruleDTO.getDeliveryFee();
            if (deliveryFee.compareTo(BigDecimal.ZERO) < NumberConstants.ZERO || deliveryFee.compareTo(NumberConstants.FOUR_NINE) > NumberConstants.ZERO) {
                return ResultDTOEnum.DELIVERY_FEE_ERROR;
            }
            if (deliveryFee.scale() > NumberConstants.TWO) {
                return ResultDTOEnum.DELIVERY_FEE_SCALE_ERROR;
            }
            MerchantDeliveryFeeRule rule = new MerchantDeliveryFeeRule();
            BeanUtils.copyProperties(ruleDTO, rule);
            rule.setDefaultType(DefaultTypeEnum.NO_DEFAULT.getCode());
            rule.setTenantId(tenantId);
            rule.setRuleType(Constants.ORDER_RULE_TYPE);
            // 实时加价运费的默认费用0
            rule.setRelateNumber(BigDecimal.ZERO);
            rule.setType(WarehouseTypeEnum.PROPRIETARY.getCode());
            rule.setPriceType(Constants.DEFAULT_PRICE_TYPE);
            merchantDeliveryFeeRuleMapper.insertSelective(rule);
            // 新增关联数据
            merchantDeliveryRuleWarehouseRelationService.batchInsert(rule.getId(), ruleDTO.getWarehouseNoList(), tenantId);
        }
        return null;
    }

    private void dealRelationData(List<MerchantDeliveryRuleWarehouseRelation> dbList, MerchantDeliveryFeeSubRuleDTO ruleDTO, Long tenantId) {
        // 存储的仓库编号列表
        List<Integer> dbNoList = Optional.ofNullable(dbList).orElse(Lists.newArrayList()).stream().map(MerchantDeliveryRuleWarehouseRelation::getWarehouseNo).collect(Collectors.toList());
        // 入参的仓库编号列表
        List<Integer> newWarehouseNoList = ruleDTO.getWarehouseNoList();
        List<Integer> tempList = Lists.newArrayList();
        tempList.addAll(newWarehouseNoList);

        // 新添加的
        newWarehouseNoList.removeAll(dbNoList);
        // 需删除的
        dbNoList.removeAll(tempList);
        merchantDeliveryRuleWarehouseRelationService.batchInsert(ruleDTO.getId(), newWarehouseNoList, tenantId);
        merchantDeliveryRuleWarehouseRelationService.deleteByParam(ruleDTO.getId(), dbNoList, tenantId);
    }

    @Override
    public ResultDTO<TenantDeliveryFeeRuleDTO> queryTenantDeliveryRule(LoginContextInfoDTO loginContextInfoDTO) {
        TenantDeliveryFeeRuleDTO tenantDeliveryFeeRuleDTO = tenantDeliveryFeeRuleService.queryTenantDeliveryFeeRule(loginContextInfoDTO.getTenantId());
        return ResultDTO.success(tenantDeliveryFeeRuleDTO);
    }

    @Override
    public Boolean updateDeliveryStepFeeRule(DeliveryFeeRuleEditWrapperDTO deliveryFeeRule) {
        List<MerchantDeliveryFeeRuleEditDTO> feeRuleList = deliveryFeeRule.getFeeRuleList();
        if (CollectionUtil.isEmpty(feeRuleList)) {
            return false;
        }
        for (MerchantDeliveryFeeRuleEditDTO feeRuleEditDTO : feeRuleList) {
            Integer type = feeRuleEditDTO.getType();
            deliveryFeeRuleContext.process(type).saveDeliveryFeeRule(feeRuleEditDTO);
        }
        return true;
    }


    @Override
    public List<MerchantDeliveryFeeRuleVO> listRule(Long tenantId) {
        List<MerchantDeliveryFeeRuleVO> result = new ArrayList<>();
        for (WarehouseTypeEnum warehouseTypeEnum : Lists.newArrayList(WarehouseTypeEnum.NO_WAREHOUSE)) {
            result.add(deliveryFeeRuleContext.process(warehouseTypeEnum.getCode()).getRule(tenantId));
        }
        return result;
    }

    @Override
    public void initDeliveryStepFeeRule(List<Long> tenantIds) {
        List<MerchantDeliveryFeeRule> ruleList = merchantDeliveryFeeRuleMapper.listRuleByTenant(tenantIds);
        if (CollectionUtil.isEmpty(ruleList)) {
            log.info("{}未查询到规则。", JSON.toJSONString(tenantIds));
        }

        List<Long> queryTenantIds = ruleList.stream().map(MerchantDeliveryFeeRule::getTenantId).collect(Collectors.toList());
        Map<Long, List<MerchantDeliveryStepFee>> stepFeeTenantMap = merchantDeliveryStepFeeRepository.queryStepFee(queryTenantIds);
        Map<String, List<MerchantDeliveryFeeRule>> ruleMap = ruleList.stream().collect(Collectors.groupingBy(i -> i.getTenantId() + "_" + i.getType()));

        List<MerchantDeliveryRuleWarehouseRelation> warehouseRelationList = merchantDeliveryRuleWarehouseRelationService.queryByTenantId(queryTenantIds);
        Map<String, List<MerchantDeliveryRuleWarehouseRelation>> warehouseRelationMap = warehouseRelationList.stream().collect(Collectors.groupingBy(i -> i.getTenantId() + "_" + i.getRuleId()));

        for (String key : ruleMap.keySet()) {
            log.info(">>>> key:{}", key);
            // 如果已经生成了阶梯价，则不进行初始化
            String[] s = key.split("_");
            if (CollectionUtil.isNotEmpty(stepFeeTenantMap.get(Long.valueOf(s[0])))) {
                continue;
            }
            List<MerchantDeliveryFeeRule> ruleTypeList = ruleMap.get(key);
            ruleTypeList.sort(Comparator.comparing(MerchantDeliveryFeeRule::getDefaultType).reversed());

            MerchantDeliveryFeeRule defaultRule = ruleTypeList.get(0);
            List<MerchantDeliveryFeeRuleEditDTO> specialRuleList = new ArrayList<>();
            for (int i = 1; i < ruleTypeList.size(); i++) {
                List<MerchantDeliveryStepFeeVO> specialStepFees = buildStepFee(ruleTypeList.get(i));
                MerchantDeliveryFeeRuleEditDTO specialEditRule = MerchantDeliveryFeeRuleConvert.INSTANCE.convert2EditDTO(ruleTypeList.get(i));
                specialEditRule.setStepFeeList(specialStepFees);
                List<MerchantDeliveryRuleWarehouseRelation> warehouseRelaList = warehouseRelationMap.get(specialEditRule.getTenantId() + "_" + specialEditRule.getId());
                if (CollectionUtil.isNotEmpty(warehouseRelaList)) {
                    specialEditRule.setWarehouseNoList(warehouseRelaList.stream().map(MerchantDeliveryRuleWarehouseRelation::getWarehouseNo).collect(Collectors.toList()));
                }
                // 例外规则，填充当前所有商品、所有区域
                specialEditRule.setAllItemHit(true);
                specialRuleList.add(specialEditRule);
            }

            List<MerchantDeliveryStepFeeVO> stepFeeVOS = buildStepFee(defaultRule);
            MerchantDeliveryFeeRuleEditDTO editDTO = MerchantDeliveryFeeRuleConvert.INSTANCE.convert2EditDTO(defaultRule);
            Integer type = editDTO.getType();
            editDTO.setStepFeeList(stepFeeVOS);
            editDTO.setSpecialDeliveryFeeRule(specialRuleList);
            List<MerchantDeliveryRuleWarehouseRelation> warehouseRelaList = warehouseRelationMap.get(editDTO.getTenantId() + "_" + editDTO.getId());
            if (CollectionUtil.isNotEmpty(warehouseRelaList)) {
                editDTO.setWarehouseNoList(warehouseRelaList.stream().map(MerchantDeliveryRuleWarehouseRelation::getWarehouseNo).collect(Collectors.toList()));
            }

            deliveryFeeRuleContext.process(type).saveDeliveryFeeRule(editDTO);
        }
    }

    private List<MerchantDeliveryStepFeeVO> buildStepFee(MerchantDeliveryFeeRule defaultRule) {
        List<MerchantDeliveryStepFeeVO> stepFeeVOS = new ArrayList<>();
        MerchantDeliveryStepFeeVO first = new MerchantDeliveryStepFeeVO();
        first.setFeeRule(defaultRule.getFreeDeliveryType());
        // 元门槛为0，件门槛为1
        first.setStepThreshold(new BigDecimal(defaultRule.getFreeDeliveryType()));
        first.setDeliveryFee(Optional.ofNullable(defaultRule.getDeliveryFee()).orElse(BigDecimal.ZERO));
        stepFeeVOS.add(first);
        if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(defaultRule.getType()) && MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.FOLLOW_WAREHOUSE.getCode().equals(defaultRule.getRuleType())) {
            return stepFeeVOS;
        }

        // 生产有部分数据没有门槛直接免运，只需要生成一条阶梯
        if (first.getDeliveryFee().compareTo(BigDecimal.ZERO) != 0) {
            MerchantDeliveryStepFeeVO second = new MerchantDeliveryStepFeeVO();
            second.setFeeRule(defaultRule.getFreeDeliveryType());
            // 门槛类型 0金额，1数量
            second.setStepThreshold(NumberConstant.ZERO.equals(defaultRule.getFreeDeliveryType()) ? defaultRule.getFreeDeliveryPrice() : new BigDecimal(Optional.ofNullable(defaultRule.getFreeDeliveryQuantity()).orElse(1)));
            second.setDeliveryFee(BigDecimal.ZERO);
            stepFeeVOS.add(second);
        }
        return stepFeeVOS;
    }

    @Override
    public void updateDeliveryFeeRuleWithItemDelete(Long tenantId, Long marketItemId) {
        List<MerchantDeliveryFeeRuleVO> merchantDeliveryFeeRuleVOS = listRule(tenantId);
        // 只有特例规则会有 hitItemIds
        List<MerchantDeliveryFeeRuleVO> specialDeliveryFeeRule = new ArrayList<>();
        merchantDeliveryFeeRuleVOS.forEach(rule -> {
            if (CollectionUtils.isEmpty(rule.getSpecialDeliveryFeeRule())) {
                return;
            }
            specialDeliveryFeeRule.addAll(rule.getSpecialDeliveryFeeRule());
        });

        List<MerchantDeliveryFeeRuleVO> needUpdateRule = specialDeliveryFeeRule.stream()
                .filter(rule -> !CollectionUtils.isEmpty(rule.getHitItemIds()) && rule.getHitItemIds().contains(marketItemId))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needUpdateRule)) {
            return;
        }
        needUpdateRule.forEach(rule -> {
            MerchantDeliveryFeeRule updateRule = new MerchantDeliveryFeeRule();
            updateRule.setId(rule.getId());
            // 调整set
            rule.getHitItemIds().remove(marketItemId);
            updateRule.setHitItemIds(rule.getHitItemIds());
            merchantDeliveryFeeRuleMapper.updateByPrimaryKeySelective(updateRule);
        });
    }
}
