package com.cosfo.manage.merchant.service;

import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.merchant.model.dto.LoginDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.merchant.model.dto.MerchantDTO;
import com.cosfo.manage.merchant.model.dto.SettleMentInfoDTO;
import com.cosfo.manage.merchant.model.po.Merchant;
import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.client.tenant.resp.MerchantResultResp;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2022/5/7  11:12
 */
public interface MerchantService {
    /**
     * 登录
     *
     * @param loginDTO
     * @return
     */
    ResultDTO login(LoginDTO loginDTO);

    /**
     * 获取品牌方信息
     *
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO getMchMerchantInfo(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 登出
     *
     * @param request
     * @return
     */
    ResultDTO loginOut(HttpServletRequest request);

    /**
     * 获取安全信息
     *
     * @return
     */
    ResultDTO getSafeInfo(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 租户信息修改
     *
     * @return
     */
    ResultDTO merchantUpdate(MerchantDTO merchantDTO, LoginContextInfoDTO contextInfoDTO);


    /**
     * 根据id查询
     * @param tenantId
     * @return
     */
    MerchantResultResp selectByTenantId(Long tenantId);
}
