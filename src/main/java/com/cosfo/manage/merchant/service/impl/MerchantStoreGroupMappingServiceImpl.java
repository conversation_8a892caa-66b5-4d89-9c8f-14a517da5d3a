package com.cosfo.manage.merchant.service.impl;

import com.cosfo.manage.common.constant.ResultErrorStringConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreGroupFacade;
import com.cosfo.manage.merchant.convert.MerchantStoreMapperConvert;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryAddressResultDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreQueryDTO;
import com.cosfo.manage.merchant.service.MerchantAddressService;
import com.cosfo.manage.merchant.service.MerchantStoreGroupMappingService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStorePageQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/2/20 21:47
 */
@Service
public class MerchantStoreGroupMappingServiceImpl implements MerchantStoreGroupMappingService {

//    @Resource
//    private MerchantStoreGroupMappingRepository merchantStoreGroupMappingRepository;
//    @Resource
//    private MerchantStoreGroupMappingMapper merchantStoreGroupMappingMapper;
//    @Resource
//    private MerchantAddressMapper merchantAddressMapper;
//    @Resource
//    private MerchantStoreGroupRepository merchantStoreGroupRepository;
//    @Resource
//    private ProductPricingSupplyService productPricingSupplyService;
//    @Resource
//    private MerchantContactMapper merchantContactMapper;
//    @Resource
//    private MerchantStoreMapper merchantStoreMapper;
    @Resource
    private MerchantAddressService merchantAddressService;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private UserCenterMerchantStoreGroupFacade userCenterMerchantStoreGroupFacade;


    @Override
    public List<MerchantStoreGroupResultResp> selectByGroupId(List<Long> groupIds, Long tenantId) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        return userCenterMerchantStoreGroupFacade.getGroupByStoreGroupIds(tenantId, groupIds);
//        return merchantStoreGroupMappingRepository.selectByGroupId(groupIds, tenantId);
    }

    @Override
    public CommonResult<PageInfo<MerchantStoreDTO>> listAll(MerchantStoreQueryDTO merchantStoreQueryDTO, LoginContextInfoDTO contextInfoDTO) {
        Long tenantId = contextInfoDTO.getTenantId();
        merchantStoreQueryDTO.setTenantId(tenantId);
        // 通过group_id查询
        List<Long> groupIdList = new LinkedList<>();
        if (ObjectUtils.isEmpty(merchantStoreQueryDTO.getGroupId())){
            throw new ProviderException(ResultDTOEnum.STORE_NO_EXISTS.getMessage(), ResultDTOEnum.STORE_NO_EXISTS.getCode(), ResultErrorStringConstants.STORE_NO_EXISTS);
        }
        groupIdList.add(merchantStoreQueryDTO.getGroupId());
        AssertCheckParams.notNull(merchantStoreQueryDTO.getPageIndex(), ResultDTOEnum.NO_PAGE_PARAMS.getCode(), ResultDTOEnum.NO_PAGE_PARAMS.getMessage());
        AssertCheckParams.notNull(merchantStoreQueryDTO.getPageSize(), ResultDTOEnum.NO_PAGE_PARAMS.getCode(), ResultDTOEnum.NO_PAGE_PARAMS.getMessage());
//        PageHelper.startPage(merchantStoreQueryDTO.getPageIndex(), merchantStoreQueryDTO.getPageSize());
//        List<MerchantStoreDTO> storeDTOList = merchantStoreGroupMappingMapper.listAll(merchantStoreQueryDTO);

        MerchantStorePageQueryReq queryReq = new MerchantStorePageQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setId(merchantStoreQueryDTO.getId());
        queryReq.setStoreNo(merchantStoreQueryDTO.getStoreNo());
        queryReq.setStoreName(merchantStoreQueryDTO.getStoreName());
        queryReq.setStatus(merchantStoreQueryDTO.getStatus());
        queryReq.setType(merchantStoreQueryDTO.getType());
        queryReq.setStoreIds(merchantStoreQueryDTO.getStoreIds());
        queryReq.setProvince(merchantStoreQueryDTO.getProvince());
        queryReq.setCity(merchantStoreQueryDTO.getCity());
        queryReq.setArea(merchantStoreQueryDTO.getArea());
        queryReq.setGroupId(merchantStoreQueryDTO.getGroupId());

        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(merchantStoreQueryDTO.getPageIndex());
        pageQueryReq.setPageSize(merchantStoreQueryDTO.getPageSize());

        PageInfo<MerchantStoreAndAddressResultResp> merchantStoreAndAddressPage = userCenterMerchantStoreFacade.getMerchantStoreAndAddressPage(queryReq, pageQueryReq);
        List<MerchantStoreAndAddressResultResp> list = merchantStoreAndAddressPage.getList();
        List<MerchantStoreDTO> storeDTOList = MerchantStoreMapperConvert.INSTANCE.storeAddressRespListToDtoList(list);
//        return CommonResult.ok(PageInfoHelper.createPageInfo(storeDTOList, merchantStoreQueryDTO.getPageSize()));
        return CommonResult.ok(PageInfoConverter.toPageInfoTransfer(merchantStoreAndAddressPage, storeDTOList));
    }

    public Map<Long, MerchantDeliveryAddressResultDTO> getMerchantAddress(List<MerchantStoreDTO> storeDTOList, Long tenantId){
        List<Long> storeIds = storeDTOList.stream().map(MerchantStoreDTO::getId).collect(Collectors.toList());
        // 查询地址和默认联系人
        List<MerchantDeliveryAddressResultDTO> merchantAddressDTOS = merchantAddressService.selectByStoreIds(tenantId, storeIds);
        Map<Long, MerchantDeliveryAddressResultDTO> merchantAddressDtoMap = merchantAddressDTOS.stream().collect(Collectors.toMap(MerchantDeliveryAddressResultDTO::getStoreId, item -> item, (v1, v2) -> v1));
        return merchantAddressDtoMap;
    }



    @Override
    public CommonResult<List<MerchantStoreDTO>> listByTenantId(Long groupId, LoginContextInfoDTO loginContextInfoDTO) {
        Long tenantId = loginContextInfoDTO.getTenantId();
        List<Long> groupList = new LinkedList<>();
        groupList.add(groupId);
        List<MerchantStoreGroupResultResp> groupMappings = selectByGroupId(groupList, loginContextInfoDTO.getTenantId());
        List<Long> storeIdList = new LinkedList<>();
        // 该分组已关联的门店id
        if (!ObjectUtils.isEmpty(groupMappings)){
            storeIdList = groupMappings.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
        }else {
            return CommonResult.ok(new LinkedList<>());
        }
        // 该分组已关联的门店信息
        List<MerchantStoreDTO> storeDTOList = MerchantStoreMapperConvert.INSTANCE.respListToMerchantStoreDtoList(userCenterMerchantStoreFacade.getMerchantStoreList(storeIdList));
//        List<MerchantStoreDTO> storeDTOList = merchantStoreMapper.batchQuery(loginContextInfoDTO.getTenantId(), storeIdList);
        Map<Long, MerchantDeliveryAddressResultDTO> merchantAddressDtoMap;
        if (!CollectionUtils.isEmpty(storeDTOList)){
            merchantAddressDtoMap = getMerchantAddress(storeDTOList, tenantId);
        }else {
            return CommonResult.ok(new LinkedList<>());
        }
        // 为这些门店设置省市区信息
        for (MerchantStoreDTO merchantStoreDTO : storeDTOList) {
            if (merchantAddressDtoMap.containsKey(merchantStoreDTO.getId())) {
                MerchantDeliveryAddressResultDTO merchantAddressDto = merchantAddressDtoMap.get(merchantStoreDTO.getId());
                merchantStoreDTO.setProvince(merchantAddressDto.getProvince());
                merchantStoreDTO.setCity(merchantAddressDto.getCity());
                merchantStoreDTO.setArea(merchantAddressDto.getArea());
                merchantStoreDTO.setAddress(merchantAddressDto.getProvince()+ StringConstants.LEFT_SLASH + merchantAddressDto.getCity() + StringConstants.LEFT_SLASH + merchantAddressDto.getArea());
                merchantStoreDTO.setDeliveryAddress(merchantAddressDto.getDeliveryAddress());
            }
        }
        return CommonResult.ok(storeDTOList);
    }
}
