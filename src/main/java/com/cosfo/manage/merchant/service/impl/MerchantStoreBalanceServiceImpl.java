package com.cosfo.manage.merchant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.DateUtil;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.BalanceRecordConstants;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.QiNiuConstants;
import com.cosfo.manage.common.context.ExcelTypeEnum;
import com.cosfo.manage.common.context.FileDownloadStatusEnum;
import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import com.cosfo.manage.common.context.MerchantStoreAccountStatusEnum;
import com.cosfo.manage.common.context.auth.AuthTypeEnum;
import com.cosfo.manage.common.context.store.balance.BalanceAuthorityTypeEnum;
import com.cosfo.manage.common.context.store.balance.BalanceChangeTypeEnum;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.LocalDateTimeUtil;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import com.cosfo.manage.facade.ordercenter.OrderAfterSaleQueryFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.file.model.po.FileDownloadRecord;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import com.cosfo.manage.merchant.mapper.MerchantStoreBalanceChangeRecordMapper;
import com.cosfo.manage.merchant.mapper.MerchantStoreBalanceMapper;
import com.cosfo.manage.merchant.model.dto.AuthQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.manage.merchant.model.dto.balance.BalanceChangeRecordExportDTO;
import com.cosfo.manage.merchant.model.dto.balance.BalanceRecordDTO;
import com.cosfo.manage.merchant.model.dto.balance.MerchantStoreBalanceDTO;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalance;
import com.cosfo.manage.merchant.model.po.MerchantStoreBalanceChangeRecord;
import com.cosfo.manage.merchant.model.vo.balance.BalanceChangeRecordVO;
import com.cosfo.manage.merchant.model.vo.balance.BalanceOverviewVO;
import com.cosfo.manage.merchant.service.MerchantStoreBalanceService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.cosfo.manage.order.model.dto.OrderDTO;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.cosfo.manage.tenant.model.vo.TenantAccountVO;
import com.cosfo.manage.tenant.service.AuthMenuPurviewService;
import com.cosfo.manage.tenant.service.TenantAccountService;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.AbstractPlatformTransactionManager;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MerchantStoreBalanceServiceImpl implements MerchantStoreBalanceService {
    @Resource
    private MerchantStoreService merchantStoreService;

    @Resource
    private FileDownloadRecordService fileDownloadRecordService;

    @Resource
    private MerchantStoreBalanceChangeRecordMapper merchantStoreBalanceChangeRecordMapper;

    @Resource
    private MerchantStoreBalanceMapper merchantStoreBalanceMapper;

    @Resource
    private AbstractPlatformTransactionManager transactionManager;

    @Resource
    private TenantAccountService tenantAccountService;

    @Resource
    private OrderBusinessService orderBusinessService;

    @Resource
    private AuthMenuPurviewService authMenuPurviewService;

    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;

    @Resource
    private CommonService commonService;
    @Resource
    private OrderAfterSaleQueryFacade orderAfterSaleQueryFacade;

    @Override
    public MerchantStoreBalanceChangeRecord addBalanceChangeRecord(MerchantStoreBalanceChangeRecord merchantStoreBalanceChangeRecord) {
        merchantStoreBalanceChangeRecordMapper.insert(merchantStoreBalanceChangeRecord);
        return merchantStoreBalanceChangeRecord;
    }

    @Override
    public CommonResult<Object> adjustBalance(MerchantStoreBalanceDTO merchantStoreBalanceDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Long tenantId = UserLoginContextUtils.getTenantId();
        Long authUserId = UserLoginContextUtils.getAuthUserId();
        org.apache.commons.lang3.tuple.Pair<Boolean, Boolean> pair = authMenuPurviewService.checkAuthByCache(AuthQueryDTO.builder().tenantId(tenantId).authUserId(authUserId).build(), AuthTypeEnum.MANAGER_BALANCE.getUrl(), null);
        AssertCheckParams.expectTrue(pair.getLeft(), ResultStatusEnum.SERVER_ERROR.getStatus(), "用户无调整余额权限");
        AssertCheckParams.allNotNull(ResultStatusEnum.SERVER_ERROR.getStatus(), "缺少必填参数",
                merchantStoreBalanceDTO.getStoreId(), merchantStoreBalanceDTO.getProof(), merchantStoreBalanceDTO.getChangeBalance());
        MerchantStoreDTO storeDTO = merchantStoreService.selectDetail(merchantStoreBalanceDTO.getStoreId());
        AssertCheckParams.expectTrue(Objects.nonNull(storeDTO) && Objects.equals(storeDTO.getStatus(), MerchantStoreAccountStatusEnum.AUDIT_SUCCESS.getStatus()), ResultStatusEnum.SERVER_ERROR.getStatus(), "门店尚未审核通过");

        MerchantStoreBalance merchantStoreBalance = merchantStoreBalanceMapper.selectByStoreId(loginContextInfoDTO.getTenantId(), merchantStoreBalanceDTO.getStoreId());
        if (NumberUtil.add(merchantStoreBalance.getBalance(), merchantStoreBalanceDTO.getChangeBalance()).compareTo(new BigDecimal("********.99")) > 0) {
            throw new BizException("调后余额超过最大值，请输入合理数据");
        }

        TransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            // 事务操作
            int result = merchantStoreBalanceMapper.updateBalanceByStoreId(loginContextInfoDTO.getTenantId(), merchantStoreBalanceDTO.getStoreId(), merchantStoreBalanceDTO.getChangeBalance());
            AssertCheckParams.expectTrue(result > 0, ResultStatusEnum.SERVER_ERROR.getStatus(), "门店额度不足");
            merchantStoreBalance = merchantStoreBalanceMapper.selectByStoreId(loginContextInfoDTO.getTenantId(), merchantStoreBalanceDTO.getStoreId());

            // 插入明细
            MerchantStoreBalanceChangeRecord merchantStoreBalanceChangeRecord = new MerchantStoreBalanceChangeRecord();
            merchantStoreBalanceChangeRecord.setTenantId(loginContextInfoDTO.getTenantId());
            merchantStoreBalanceChangeRecord.setStoreId(merchantStoreBalanceDTO.getStoreId());
            merchantStoreBalanceChangeRecord.setChangeBalance(merchantStoreBalanceDTO.getChangeBalance());
            merchantStoreBalanceChangeRecord.setAfterChangeBalance(merchantStoreBalance.getBalance());
            merchantStoreBalanceChangeRecord.setProof(merchantStoreBalanceDTO.getProof());
            merchantStoreBalanceChangeRecord.setRemark(merchantStoreBalanceDTO.getRemark());
            merchantStoreBalanceChangeRecord.setOperatorId(loginContextInfoDTO.getAuthUserId());
            merchantStoreBalanceChangeRecord.setType(merchantStoreBalanceDTO.getChangeBalance().compareTo(BigDecimal.ZERO) >= 0 ? BalanceChangeTypeEnum.PRE_PAY.getType() : BalanceChangeTypeEnum.DEDUCTION.getType());
            addBalanceChangeRecord(merchantStoreBalanceChangeRecord);
            transactionManager.commit(status);
        } catch (Exception e) {
            // 事务回滚
            transactionManager.rollback(status);
            throw e;
        }
        // 若门店余额是关闭状态，直接开启
        if (!Objects.equals(storeDTO.getBalanceAuthority(), BalanceAuthorityTypeEnum.OPEN_BALANCE_AUTH.getType())) {
            MerchantStoreCommandReq merchantStoreCommandReq = new MerchantStoreCommandReq();
            merchantStoreCommandReq.setId(storeDTO.getId());
            merchantStoreCommandReq.setBalanceAuthority(BalanceAuthorityTypeEnum.OPEN_BALANCE_AUTH.getType());
            userCenterMerchantStoreFacade.updateMerchantStore(merchantStoreCommandReq);
        }
        return CommonResult.ok();
    }

    @Override
    public CommonResult<BalanceOverviewVO> balanceOverview(LoginContextInfoDTO merchantInfoDTO) {
        // 查询租户下余额大于0的数据
        AssertCheckParams.expectTrue(Objects.nonNull(merchantInfoDTO) && Objects.nonNull(merchantInfoDTO.getTenantId()), ResultStatusEnum.SERVER_ERROR.getStatus(), "缺少必要信息");
        List<MerchantStoreBalance> merchantStoreBalances = merchantStoreBalanceMapper.selectList(new LambdaQueryWrapper<MerchantStoreBalance>().eq(MerchantStoreBalance::getTenantId, merchantInfoDTO.getTenantId()).gt(MerchantStoreBalance::getBalance, 0));

        BigDecimal totalBalance = merchantStoreBalances.stream()
                .map(MerchantStoreBalance::getBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BalanceOverviewVO balanceOverviewVO = new BalanceOverviewVO();
        balanceOverviewVO.setHasBalanceStoreCount(merchantStoreBalances.size());
        balanceOverviewVO.setTotalBalance(totalBalance);
        balanceOverviewVO.setCurrentDate(LocalDateTime.now());

        return CommonResult.ok(balanceOverviewVO);
    }

    /**
     * 余额变动明细记录
     *
     * @param merchantInfoDTO
     * @param balanceRecordDTO
     * @return
     */
    @Override
    public CommonResult<PageInfo<BalanceChangeRecordVO>> balanceChangeRecord(LoginContextInfoDTO merchantInfoDTO, BalanceRecordDTO balanceRecordDTO, Boolean queryOrder) {
        // 查询目标门店id列表
        List<Long> storeIdList = null;
        if (StringUtils.isNotEmpty(balanceRecordDTO.getStoreNo()) || StringUtils.isNotEmpty(balanceRecordDTO.getStoreName())) {
            storeIdList = merchantStoreService.selectIdListByParam(merchantInfoDTO.getTenantId(), balanceRecordDTO.getStoreNo(), balanceRecordDTO.getStoreName(),
                    null, null);
            if (CollectionUtils.isEmpty(storeIdList)) {
                return CommonResult.ok(PageInfoHelper.createPageInfo(Lists.newArrayList(), balanceRecordDTO.getPageSize()));
            }
        }
        balanceRecordDTO.setTenantId(merchantInfoDTO.getTenantId());
        balanceRecordDTO.setStoreIdList(storeIdList);
        builderTimeParam(balanceRecordDTO);

        // 查询变动明细
        Integer pageIndex = balanceRecordDTO.getPageIndex();
        Integer pageSize = balanceRecordDTO.getPageSize();
        Page<MerchantStoreBalanceChangeRecord> page = PageHelper.startPage(pageIndex, pageSize);
        List<MerchantStoreBalanceChangeRecord> merchantStoreBalanceChangeRecordList = merchantStoreBalanceChangeRecordMapper.list(balanceRecordDTO);
        if (CollectionUtils.isEmpty(merchantStoreBalanceChangeRecordList)) {
            return CommonResult.ok(PageInfoHelper.createPageInfo(Lists.newArrayList(), balanceRecordDTO.getPageSize()));
        }

        // 查询门店信息
        List<Long> resultStoreIdList = merchantStoreBalanceChangeRecordList.stream().map(MerchantStoreBalanceChangeRecord::getStoreId).distinct().collect(Collectors.toList());
        List<MerchantStoreDTO> merchantStoreDTOList = merchantStoreService.batchQuery(resultStoreIdList, merchantInfoDTO.getTenantId());
        Map<Long, MerchantStoreDTO> storeDTOMap = merchantStoreDTOList.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, Function.identity(), (V1, V2) -> V1));

        // 查询操作人信息
        List<Long> authUserIds = merchantStoreBalanceChangeRecordList.stream().map(MerchantStoreBalanceChangeRecord::getOperatorId).distinct().collect(Collectors.toList());
        List<TenantAccountVO> tenantAccountVOList = tenantAccountService.selectByAuthUserIds(authUserIds);
        Map<Long, TenantAccountVO> tenantAccountVOMap = tenantAccountVOList.stream().collect(Collectors.toMap(TenantAccountVO::getAuthUserId, Function.identity(), (v1, v2) -> v1));

        // 查询单号信息
        Pair<Map<String, Long>, Map<String, Long>> orderPair = null;
        if (queryOrder) {
            orderPair = queryAssociatedOrderMap(merchantStoreBalanceChangeRecordList, merchantInfoDTO);
        }
        Map<String, Long> orderNoMap = Optional.ofNullable(orderPair).map(Pair::getKey).orElse(Maps.newHashMap());
        Map<String, Long> afterSaleOrderMap = Optional.ofNullable(orderPair).map(Pair::getValue).orElse(Maps.newHashMap());

        List<BalanceChangeRecordVO> voList = merchantStoreBalanceChangeRecordList.stream().map(
                record -> {
                    BalanceChangeRecordVO balanceChangeRecordVO = new BalanceChangeRecordVO();
                    BeanUtils.copyProperties(record, balanceChangeRecordVO);
                    MerchantStoreDTO merchantStoreDTO = storeDTOMap.get(record.getStoreId());
                    balanceChangeRecordVO.setStoreName(Optional.ofNullable(merchantStoreDTO).map(MerchantStoreDTO::getStoreName).orElse(StringUtils.EMPTY));
                    balanceChangeRecordVO.setStoreNo(Optional.ofNullable(merchantStoreDTO).map(MerchantStoreDTO::getStoreNo).orElse(StringUtils.EMPTY));
                    balanceChangeRecordVO.setStoreId(Optional.ofNullable(merchantStoreDTO).map(MerchantStoreDTO::getId).orElse(null));
                    TenantAccountVO tenantAccountVO = tenantAccountVOMap.get(record.getOperatorId());
                    balanceChangeRecordVO.setOperatorPhone(Optional.ofNullable(tenantAccountVO).map(TenantAccountVO::getOperatorPhone).orElse(StringUtils.EMPTY));
                    balanceChangeRecordVO.setOperatorName(Optional.ofNullable(tenantAccountVO).map(TenantAccountVO::getNickname).orElse(StringUtils.EMPTY));
                    Long associatedOrderId = null;
                    if(BalanceChangeTypeEnum.CONSUME.getType().equals(record.getType())){
                        associatedOrderId = orderNoMap.get(record.getAssociatedOrderNo());
                    }else if(BalanceChangeTypeEnum.CONSUME_REFUND.getType().equals(record.getType())){
                        associatedOrderId = afterSaleOrderMap.get(record.getAssociatedOrderNo());
                    }
                    balanceChangeRecordVO.setAssociatedOrderId(associatedOrderId);
                    return balanceChangeRecordVO;
                }
        ).collect(Collectors.toList());

        return CommonResult.ok(PageInfoConverter.toPageInfo(page, voList));
    }

    /**
     * 处理前端未传yyyy-MM-dd后具体时分秒的问题
     * @param balanceRecordDTO
     */
    private void builderTimeParam(BalanceRecordDTO balanceRecordDTO) {
        if (Objects.nonNull(balanceRecordDTO.getStartTime())) {
            balanceRecordDTO.setStartTime(DateUtil.startOfDay(balanceRecordDTO.getStartTime()));
        }
        if (Objects.nonNull(balanceRecordDTO.getEndTime())) {
            balanceRecordDTO.setEndTime(DateUtil.endOfDay(balanceRecordDTO.getEndTime()));
        }
    }

    /**
     * 查询单号信息
     * @param merchantStoreBalanceChangeRecordList
     * @return 订单号map、售后单号map
     */
    private Pair<Map<String, Long>, Map<String, Long>> queryAssociatedOrderMap(List<MerchantStoreBalanceChangeRecord> merchantStoreBalanceChangeRecordList, LoginContextInfoDTO merchantInfoDTO) {
        // 订单
        List<String> orderNoList = merchantStoreBalanceChangeRecordList.stream().filter(merchantStoreBalanceChangeRecord -> BalanceChangeTypeEnum.CONSUME.getType().equals(merchantStoreBalanceChangeRecord.getType())
                && Objects.nonNull(merchantStoreBalanceChangeRecord.getAssociatedOrderNo())).map(MerchantStoreBalanceChangeRecord::getAssociatedOrderNo).distinct().collect(Collectors.toList());
        Map<String, Long> orderMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(orderNoList)) {
            List<OrderDTO> orderDTOS = orderBusinessService.queryByOrderNos(orderNoList, merchantInfoDTO.getTenantId());
            orderMap = orderDTOS.stream().collect(Collectors.toMap(OrderDTO::getOrderNo, OrderDTO::getId, (v1, v2) -> v1));
        }

        // 售后单
        List<String> afterSaleOrderNoList = merchantStoreBalanceChangeRecordList.stream().filter(merchantStoreBalanceChangeRecord -> BalanceChangeTypeEnum.CONSUME_REFUND.getType().equals(merchantStoreBalanceChangeRecord.getType())
                && Objects.nonNull(merchantStoreBalanceChangeRecord.getAssociatedOrderNo())).map(MerchantStoreBalanceChangeRecord::getAssociatedOrderNo).distinct().collect(Collectors.toList());
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryFacade.queryByNos(afterSaleOrderNoList);
        Map<String, Long> afterSaleOrderMap = orderAfterSaleResps.stream().filter(Objects::nonNull).collect(Collectors.toMap(OrderAfterSaleResp::getAfterSaleOrderNo, OrderAfterSaleResp::getOrderId, (v1, v2) -> v1));
        return new Pair(orderMap, afterSaleOrderMap);
    }

    @Override
    public CommonResult<Object> exportBalanceChangeRecord(LoginContextInfoDTO loginContextInfoDTO, BalanceRecordDTO balanceRecordDTO) {
        AssertCheckParams.expectTrue(Objects.nonNull(balanceRecordDTO.getStartTime()) && Objects.nonNull(balanceRecordDTO.getEndTime()), ResultStatusEnum.SERVER_ERROR.getStatus(), "请选择时间范围");
        builderTimeParam(balanceRecordDTO);
        Map<String, String> queryParamsMap = Maps.newHashMap();
        // 门店ID
        if (Objects.nonNull(balanceRecordDTO.getStoreNo())) {
            queryParamsMap.put(BalanceRecordConstants.STORE_NO, balanceRecordDTO.getStoreNo());
        }
        // 门店名称
        if (StringUtils.isNotEmpty(balanceRecordDTO.getStoreName())) {
            queryParamsMap.put(BalanceRecordConstants.STORE_NAME, balanceRecordDTO.getStoreName());
        }
        // 变动类型
        if (Objects.nonNull(balanceRecordDTO.getType())) {
            queryParamsMap.put(BalanceRecordConstants.TYPE, Optional.ofNullable(BalanceChangeTypeEnum.getByType(balanceRecordDTO.getType())).map(BalanceChangeTypeEnum::getDesc).orElse(StringUtils.EMPTY));
        }
        // 开始日期
        if (Objects.nonNull(balanceRecordDTO.getStartTime())) {
            queryParamsMap.put(BalanceRecordConstants.START_TIME, LocalDateTimeUtil.localTimeFormat(balanceRecordDTO.getStartTime()));
        }
        // 结束日期
        if (Objects.nonNull(balanceRecordDTO.getEndTime())) {
            queryParamsMap.put(BalanceRecordConstants.END_TIME, LocalDateTimeUtil.localTimeFormat(balanceRecordDTO.getEndTime()));
        }


        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.BALANCE_CHANGE_RECORD_EXPORT.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.STORE_BALANCE_CHANGE_RECORD_EXPORT.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(balanceRecordDTO, ee -> {
            // 1、表格处理
            String filePath = generateRecordExportFile(loginContextInfoDTO, balanceRecordDTO);

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("檔案讀取錯誤");
            } finally {
                commonService.deleteFile(filePath);
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });


//        //存储文件下载记录
//        FileDownloadRecord record = new FileDownloadRecord();
//        record.setTenantId(loginContextInfoDTO.getTenantId());
//        record.setParams(queryParamsMap.isEmpty() ? BalanceRecordConstants.TOTAL : JSONObject.toJSONString(queryParamsMap));
//        record.setStatus(FileDownloadStatusEnum.PROCESSING.getStatus());
//        // 设置导出类型为门店余额记录
//        record.setType(FileDownloadTypeEnum.BALANCE_CHANGE_RECORD_EXPORT.getType());
//        record.setOriginParams(JSON.toJSONString(balanceRecordDTO));
//        fileDownloadRecordService.generateFileDownloadRecord(record);
//
//        // 线程异步处理
//        ExecutorFactory.generateExcelExecutor.execute(() -> {
//            try {
//                generateRecordExportFile(loginContextInfoDTO, balanceRecordDTO, record.getId());
//            } catch (Exception e) {
//                fileDownloadRecordService.updateFailStatus(record.getId());
//                log.error("导出门店余额变动明细失败", e);
//            }
//        });


        return CommonResult.ok();
    }

    /**
     * 处理变动明细导出逻辑
     *
     * @param loginContextInfoDTO
     * @param balanceRecordDTO
     */
    private String generateRecordExportFile(LoginContextInfoDTO loginContextInfoDTO, BalanceRecordDTO balanceRecordDTO) {
        // 获取总页数
        balanceRecordDTO.setPageIndex(NumberConstants.ONE);
        balanceRecordDTO.setPageSize(NumberConstants.HUNDRED);
        CommonResult<PageInfo<BalanceChangeRecordVO>> pageInfoCommonResult = balanceChangeRecord(loginContextInfoDTO, balanceRecordDTO, Boolean.FALSE);
        int pages = pageInfoCommonResult.getData().getPages();

        // 创建excelWriter
        String filePath = ExcelUtils.tempExcelFilePath();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        ExcelWriter excelWriter = EasyExcel.write(filePath, BalanceChangeRecordExportDTO.class).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter())
                .withTemplate(ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.STORE_BALANCE_CHANGE_RECORD_EXPORT.getName())).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        excelWriter.fill(Collections.EMPTY_LIST, fillConfig, writeSheet);

        // 分页查询
        for (int pageNum = NumberConstants.ONE; pageNum <= pages; pageNum++) {
            balanceRecordDTO.setPageIndex(pageNum);
            List<BalanceChangeRecordVO> balanceChangeRecordVOList = balanceChangeRecord(loginContextInfoDTO, balanceRecordDTO, Boolean.FALSE).getData().getList();
            if (CollectionUtil.isEmpty(balanceChangeRecordVOList)) {
                break;
            }
            List<BalanceChangeRecordExportDTO> tempList = balanceChangeRecordVOList.stream().map(
                    balanceChangeRecordVO -> {
                        BalanceChangeRecordExportDTO balanceChangeRecordExportDTO = new BalanceChangeRecordExportDTO();
                        BeanUtils.copyProperties(balanceChangeRecordVO, balanceChangeRecordExportDTO);
                        balanceChangeRecordExportDTO.setStoreNo(balanceChangeRecordVO.getStoreNo());
                        balanceChangeRecordExportDTO.setTypeDesc(Optional.ofNullable(BalanceChangeTypeEnum.getByType(balanceChangeRecordVO.getType())).map(BalanceChangeTypeEnum::getDesc).orElse(StringUtils.EMPTY));
                        balanceChangeRecordExportDTO.setOperator(StringUtils.join(balanceChangeRecordVO.getOperatorName(), BalanceRecordConstants.OPERATOR_LINK_CHAR, balanceChangeRecordVO.getOperatorPhone()));
                        String[] split = Optional.ofNullable(balanceChangeRecordVO.getProof()).map(proof -> proof.split(BalanceRecordConstants.PROOF_SPLIT_CHAR)).orElse(new String[0]);
                        balanceChangeRecordExportDTO.setAttachmentOne(split.length > NumberConstants.ZERO ? QiNiuConstants.OSS_URL + split[NumberConstants.ZERO] : StringUtils.EMPTY);
                        balanceChangeRecordExportDTO.setAttachmentTwo(split.length > NumberConstants.ONE ? QiNiuConstants.OSS_URL + split[NumberConstants.ONE] : StringUtils.EMPTY);
                        balanceChangeRecordExportDTO.setAttachmentThree(split.length > NumberConstants.TWO ? QiNiuConstants.OSS_URL + split[NumberConstants.TWO] : StringUtils.EMPTY);
                        balanceChangeRecordExportDTO.setAttachmentFour(split.length > NumberConstants.THREE ? QiNiuConstants.OSS_URL + split[NumberConstants.THREE] : StringUtils.EMPTY);
                        balanceChangeRecordExportDTO.setAttachmentFive(split.length > NumberConstants.FOUR ? QiNiuConstants.OSS_URL + split[NumberConstants.FOUR] : StringUtils.EMPTY);
                        return balanceChangeRecordExportDTO;
                    }
            ).collect(Collectors.toList());
            // 分批写
            excelWriter.fill(tempList, fillConfig, writeSheet);
        }
        excelWriter.finish();

        return filePath;

        // 上传到七牛云
//        generateAndUploadExcel(ExcelTypeEnum.STORE_BALANCE_CHANGE_RECORD_EXPORT, fileDownloadRecordId, filePath);
    }

    public Boolean generateAndUploadExcel(ExcelTypeEnum excelTypeEnum, Long fileDownloadRecordId, String filePath) {
        String qiNiuFilePath = null;
        try {
            qiNiuFilePath = QiNiuUtils.uploadFile(filePath, excelTypeEnum.getDesc() + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH_mm_ss") + ".xlsx");
        } catch (IOException e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }

        //删除临时文件
        deleteFile(filePath);

        // 更新文件下载记录
        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
        fileDownloadRecord.setId(fileDownloadRecordId);

        Boolean successUploadFlag = Objects.nonNull(qiNiuFilePath);
        if (!successUploadFlag) {
            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FAIL.getStatus());
        } else {
            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FINISHED.getStatus());
            fileDownloadRecord.setUrl(qiNiuFilePath);
        }
        fileDownloadRecordService.updateSelectiveByPrimaryKey(fileDownloadRecord);
        return successUploadFlag;
    }

    /**
     * 文件删除
     *
     * @param filePath 文件路径
     */
    public void deleteFile(String filePath) {
        try {
            Files.delete(Paths.get(filePath));
        } catch (IOException e) {
            log.warn("filePath:{}檔案刪除失敗!", filePath, e);
        }
    }
}
