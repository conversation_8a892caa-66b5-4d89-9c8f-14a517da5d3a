package com.cosfo.manage.merchant.service;

import com.cosfo.manage.common.context.DefaultFlagEnum;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.merchant.model.dto.MerchantContactDTO;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/24 16:22
 */
public interface MerchantContactService {

    /**
     * 删除联系人
     * @param storeId
     * @param contactId
     * @return
     */
    ResultDTO deleteContact(Long storeId, Long contactId);

    /**
     * 查询门店联系人信息
     * @param tenantId
     * @param storeId
     * @return
     */
    List<MerchantContactResultResp> selectByStoreId(Long tenantId, Long storeId);

    /**
     * 批量查询店铺联系人信息
     * @param tenantId
     * @param storeIds
     * @param defaultFlagEnum
     * @return
     */
    List<MerchantContactResultResp> selectByAddressIds(Long tenantId, List<Long> storeIds, DefaultFlagEnum defaultFlagEnum);


}
