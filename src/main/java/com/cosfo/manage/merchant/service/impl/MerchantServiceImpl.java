package com.cosfo.manage.merchant.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.AESUtils;
import com.cosfo.manage.facade.usercenter.UserCenterBusinessInfoFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantInfoFacade;
import com.cosfo.manage.merchant.model.dto.LoginDTO;
import com.cosfo.manage.merchant.model.dto.MerchantDTO;
import com.cosfo.manage.merchant.model.vo.SafeInfoVO;
import com.cosfo.manage.merchant.model.vo.TenantDetailVO;
import com.cosfo.manage.merchant.service.MerchantService;
import com.github.pagehelper.util.StringUtil;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;
import net.xianmu.usercenter.client.tenant.req.MerchantCommandReq;
import net.xianmu.usercenter.client.tenant.resp.MerchantResultResp;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/5/7  11:12
 */
@Slf4j
@Service
public class MerchantServiceImpl implements MerchantService {
    @Resource
    private UserCenterMerchantInfoFacade userCenterMerchantInfoFacade;
    @Resource
    private UserCenterBusinessInfoFacade userCenterBusinessInfoFacade;
    @Value("${h5.mall.url}")
    private String H5mallUrl;

    @Override
    public ResultDTO login(LoginDTO loginDTO) {
//        AssertCheckParams.hasText(loginDTO.getPhone(), "手机号不能为空");
//        AssertCheckParams.hasText(loginDTO.getPassword(), "密码不能为空");
//        String phone = loginDTO.getPhone();
//        String password = loginDTO.getPassword();
//        // 根据手机号查询账号信息
//        Tenant tenant = tenantMapper.selectByPhone(phone);
//        if (tenant == null || !tenant.getPassword().equals(MD5Util.string2MD5(password))) {
//            return new ResultDTO(ResultDTOEnum.USER_OR_PASSWORD_WRONG);
//        }
//
//        if (!tenant.getStatus().equals(TenantStatusEnum.AUDIT_SUCCESS.getCode())) {
//            return new ResultDTO(ResultDTOEnum.ACCOUNT_FAILURE);
//        }
//
//        Merchant merchant = mchMerchantMapper.selectByTenantId(tenant.getId());
//        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
//        loginContextInfoDTO.setMId(merchant.getId());
//        loginContextInfoDTO.setTenantId(merchant.getTenantId());
//        loginContextInfoDTO.setPhone(tenant.getPhone());
//        loginContextInfoDTO.setType(tenant.getType());
//        loginContextInfoDTO.setTenantName(tenant.getTenantName());
//        // 密码强度
//        loginContextInfoDTO.setLevel(MD5Util.passwordLevel(password));
//        // 生成随机数
//        String randomNum = MD5Util.createRandomNum();
//        // 生成jwt token
//        String jwt = JwtUtils.generateToken(tenant.getId(), phone, randomNum);
//        // 简化token长度
//        String accessToken = JwtUtils.TOKEN_PREFIX + MD5Util.string2MD5(jwt);
//        loginContextInfoDTO.setJwtToken(jwt);
//        // 数据缓存
//        redisUtils.set(accessToken, loginContextInfoDTO, JwtUtils.EXPIRATION_TIME);
//
//        Map<String, String> map = new HashMap<>();
//        map.put(JwtUtils.HEADER_STRING, accessToken);
//        return ResultDTO.success(map);
        return null;
    }

    @Override
    public ResultDTO getMchMerchantInfo(LoginContextInfoDTO loginContextInfoDTO) {
        log.error("登录参数：{}", JSONObject.toJSONString(loginContextInfoDTO));
        if (Objects.isNull(loginContextInfoDTO)) {
            return ResultDTO.fail(ResultDTOEnum.SERVER_ERROR);
        }
        MerchantResultResp merchantResultResp = userCenterMerchantInfoFacade.getMerchantByTenantId(loginContextInfoDTO.getTenantId());
        TenantDetailVO tenantDetailVO = new TenantDetailVO();
        if (Objects.nonNull(merchantResultResp)) {
            tenantDetailVO.setMerchantName(merchantResultResp.getMerchantName());
            tenantDetailVO.setBackgroundImage(merchantResultResp.getBackgroundImage());
            tenantDetailVO.setLogoImage(merchantResultResp.getLogoImage());
        }
        BusinessInformationResultResp businessInformationResultResp = userCenterBusinessInfoFacade.getBusinessInfo(loginContextInfoDTO.getTenantId());
        if (Objects.nonNull(businessInformationResultResp)) {
            tenantDetailVO.setCompanyName(businessInformationResultResp.getCompanyName());
            tenantDetailVO.setCreditCode(businessInformationResultResp.getCreditCode());
            tenantDetailVO.setBusinessLicense(businessInformationResultResp.getBusinessLicense());
            tenantDetailVO.setProvince(businessInformationResultResp.getProvince());
            tenantDetailVO.setCity(businessInformationResultResp.getCity());
            tenantDetailVO.setArea(businessInformationResultResp.getArea());
            tenantDetailVO.setAddress(businessInformationResultResp.getAddress());
            tenantDetailVO.setCompanyPhone(businessInformationResultResp.getPhone());
        }
//        TenantDetailVO tenantDetailVO = mchMerchantMapper.selectByMId(loginContextInfoDTO.getTenantId());
//        if (Objects.isNull(loginContextInfoDTO)) {
//            return ResultDTO.fail(ResultDTOEnum.SERVER_ERROR);
//        }

        tenantDetailVO.setTenantId(loginContextInfoDTO.getTenantId());
        tenantDetailVO.setPhone(loginContextInfoDTO.getPhone());
        String token = AESUtils.encrypt(loginContextInfoDTO.getTenantId().toString());
        tenantDetailVO.setMallUrl(H5mallUrl + token);
        return ResultDTO.success(tenantDetailVO);
    }

    @Override
    public ResultDTO loginOut(HttpServletRequest request) {
        Subject subject = SecurityUtils.getSubject();
        subject.logout();
        return ResultDTO.success();
    }

    @Override
    public ResultDTO getSafeInfo(LoginContextInfoDTO loginContextInfoDTO) {
        SafeInfoVO safeInfoVO = new SafeInfoVO();
        BeanUtils.copyProperties(loginContextInfoDTO, safeInfoVO);
        return ResultDTO.success(safeInfoVO);
    }

    @Override
    public ResultDTO merchantUpdate(MerchantDTO merchantDTO, LoginContextInfoDTO contextInfoDTO) {
        MerchantResultResp merchant = selectByTenantId(contextInfoDTO.getTenantId());
        if (Objects.isNull(merchant)) {
            return ResultDTO.fail(ResultDTOEnum.SERVER_ERROR);
        }
        MerchantCommandReq commandReq = new MerchantCommandReq();
        commandReq.setId(merchant.getId());
        commandReq.setTenantId(merchant.getTenantId());
        if (StringUtil.isNotEmpty(merchantDTO.getLogoImage())){
            commandReq.setLogoImage(merchantDTO.getLogoImage());
        }
        if (StringUtil.isNotEmpty(merchantDTO.getBackgroundImage())){
            commandReq.setBackgroundImage(merchantDTO.getBackgroundImage());
        }
        if (StringUtil.isNotEmpty(merchantDTO.getMerchantName())){
            commandReq.setMerchantName(merchantDTO.getMerchantName());
        }
//        mchMerchantMapper.updateByPrimaryKeySelective(merchant);
        Boolean update = userCenterMerchantInfoFacade.update(commandReq);
        if (!Boolean.TRUE.equals(update)) {
            return ResultDTO.fail(ResultDTOEnum.OPERATION_FAIL);
        }
        return  ResultDTO.success("修改成功");
    }

    @Override
    public MerchantResultResp selectByTenantId(Long tenantId) {
        return userCenterMerchantInfoFacade.getMerchantByTenantId(tenantId);
//        return mchMerchantMapper.selectByTenantId(tenantId);
    }
}
