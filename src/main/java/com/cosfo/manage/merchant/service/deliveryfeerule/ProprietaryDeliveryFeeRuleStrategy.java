package com.cosfo.manage.merchant.service.deliveryfeerule;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.manage.common.context.WarehouseTypeEnum;
import com.cosfo.manage.merchant.model.dto.MerchantDeliveryFeeRuleEditDTO;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryFeeRule;
import com.cosfo.manage.merchant.model.po.MerchantDeliveryRuleWarehouseRelation;
import com.cosfo.manage.merchant.model.vo.MerchantDeliveryFeeRuleVO;
import com.cosfo.manage.merchant.service.MerchantDeliveryRuleWarehouseRelationService;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProprietaryDeliveryFeeRuleStrategy implements DeliveryFeeRuleStrategy {
    @Resource
    private MerchantDeliveryRuleWarehouseRelationService merchantDeliveryRuleWarehouseRelationService;

    @Resource
    private CommonFeeRuleHandle commonFeeRuleHandle;


    @Override
    public Integer type() {
        return WarehouseTypeEnum.PROPRIETARY.getCode();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveDeliveryFeeRule(MerchantDeliveryFeeRuleEditDTO feeRule) {
        // 统一层级处理
        List<MerchantDeliveryFeeRuleEditDTO> roleList = commonFeeRuleHandle.unFoldRule(feeRule);
        commonFeeRuleHandle.removeSpecialRule(feeRule.getTenantId(), type(), roleList.stream().map(MerchantDeliveryFeeRuleEditDTO::getId).filter(Objects::nonNull).collect(Collectors.toList()));

        List<Long> allItemIds = commonFeeRuleHandle.queryAllItemIds(feeRule.getTenantId(), roleList);

        for (int i = 0; i < roleList.size(); i++) {
            MerchantDeliveryFeeRuleEditDTO ruleEditDTO = roleList.get(i);
            MerchantDeliveryFeeRule deliveryFeeRule = commonFeeRuleHandle.handleCommonRule(ruleEditDTO, allItemIds, i);
            // 更新仓
            List<Integer> warehouseNoList = ruleEditDTO.getWarehouseNoList();
            merchantDeliveryRuleWarehouseRelationService.deleteByRuleIds(Lists.newArrayList(deliveryFeeRule.getId()), feeRule.getTenantId());
            // 删除后新增
            // 外层batch insert
            // TODO:[zhoujiachen] 批量插入
            merchantDeliveryRuleWarehouseRelationService.batchInsert(deliveryFeeRule.getId(), warehouseNoList, feeRule.getTenantId());
        }
        return true;
    }

    @Override
    public MerchantDeliveryFeeRuleVO getRule(Long tenantId) {
        MerchantDeliveryFeeRuleVO result = commonFeeRuleHandle.handleCommonRule(tenantId, type());
        List<MerchantDeliveryFeeRuleVO> specialDeliveryFeeRule = result.getSpecialDeliveryFeeRule();
        if (CollectionUtils.isEmpty(specialDeliveryFeeRule)) {
            return result;
        }
        // 补充仓信息
        List<MerchantDeliveryRuleWarehouseRelation> relations = merchantDeliveryRuleWarehouseRelationService.queryByRuleIds(tenantId, specialDeliveryFeeRule.stream().map(MerchantDeliveryFeeRuleVO::getId).collect(Collectors.toList()));
        Map<Long, List<MerchantDeliveryRuleWarehouseRelation>> ruleIdMap = relations.stream().collect(Collectors.groupingBy(MerchantDeliveryRuleWarehouseRelation::getRuleId));
        Map<Integer, String> warehouseStorageMap = Collections.EMPTY_MAP;
        specialDeliveryFeeRule.forEach(feeRule -> {
            // 获取仓
            List<MerchantDeliveryRuleWarehouseRelation> list = ruleIdMap.get(feeRule.getId());
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            List<Integer> warehouseNoList = list.stream().sorted(Comparator.comparing(MerchantDeliveryRuleWarehouseRelation::getWarehouseNo))
                    .map(MerchantDeliveryRuleWarehouseRelation::getWarehouseNo).collect(Collectors.toList());
            feeRule.setWarehouseNoList(warehouseNoList);
            List<String> warehouseNameList = getWarehouseNameList(warehouseStorageMap, warehouseNoList);
            feeRule.setWarehouseNameList(warehouseNameList);
        });
        return result;
    }

    private List<String> getWarehouseNameList(Map<Integer, String> warehouseStorageMap, List<Integer> warehouseNo) {
        if (CollectionUtil.isEmpty(warehouseNo)) {
            return Lists.newArrayList();
        }
        return warehouseNo.stream().filter(no -> Objects.nonNull(warehouseStorageMap.get(no))).map(no -> warehouseStorageMap.get(no)).collect(Collectors.toList());
    }
}
