package com.cosfo.manage.merchant.service.impl;

import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.DefaultFlagEnum;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantContactFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.merchant.convert.MerchantStoreMapperConvert;
import com.cosfo.manage.merchant.model.po.MerchantStore;
import com.cosfo.manage.merchant.service.MerchantContactService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.log.config.BizLogRecordContext;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/24 16:22
 */
@Service
@Slf4j
public class MerchantContactServiceImpl implements MerchantContactService {

    //    @Resource
//    private MerchantContactMapper merchantContactMapper;
//    @Resource
//    private MerchantStoreMapper merchantStoreMapper;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;
    @Resource
    private UserCenterMerchantContactFacade userCenterMerchantContactFacade;

    @Override
    public ResultDTO deleteContact(Long storeId, Long contactId) {
//        MerchantStore store = merchantStoreMapper.selectByPrimaryKey(storeId);
        MerchantStore store = MerchantStoreMapperConvert.INSTANCE.respToMerchantStore(userCenterMerchantStoreFacade.getMerchantStoreById(storeId));
        List<MerchantContactResultResp> contactList = selectByStoreId(store.getTenantId(), storeId);
        if (CollectionUtils.isEmpty(contactList)) {
            return ResultDTO.fail(ResultDTOEnum.CONTACT_NOT_FOUND);
        }
        if (contactList.size() == NumberConstants.ONE) {
            return ResultDTO.fail(ResultDTOEnum.CONTACT_AT_LEAST_ONE);
        }
        MerchantContactCommandReq req = new MerchantContactCommandReq();
        req.setId(contactId);
        Boolean success = userCenterMerchantContactFacade.remove(req);
        if (!Boolean.TRUE.equals(success)) {
            return ResultDTO.fail(ResultDTOEnum.OPERATION_FAIL);
        }
//        merchantContactMapper.deleteByPrimaryKey(contactId);
        log.info("门店：{}的联系人id：{}删除成功", storeId, contactId);
        // 获取日志上下文
        buildDeleteContactContext(contactList, storeId, contactId);
        return ResultDTO.success();
    }

    private void buildDeleteContactContext(List<MerchantContactResultResp> contactList, Long storeId, Long contactId) {
        try {
            BizLogRecordContext.put("storeId", storeId);
            BizLogRecordContext.put("tenantId", UserLoginContextUtils.getTenantId());
            MerchantContactResultResp merchantContactResultResp = contactList.stream().filter(i -> contactId.equals(i.getId())).findFirst().orElse(null);
            Map<String, Object> content = new HashMap<>();
            if (Objects.isNull(merchantContactResultResp)) {
                log.warn("未找到被删除的联系人,storeId:{},contactId:{}", storeId, contactId);
            } else {
                content.put("deleteContact", merchantContactResultResp);
            }
            BizLogRecordContext.put("content", content);
        } catch (Exception e) {
            log.error("获取日志上下文失败！", e);
        }
    }

    @Override
    public List<MerchantContactResultResp> selectByStoreId(Long tenantId, Long storeId) {
        List<MerchantContactResultResp> merchantContactResultResps = userCenterMerchantContactFacade.getMerchantContactsByStoreId(tenantId, storeId);
        if (Objects.isNull(merchantContactResultResps)) {
            return Collections.emptyList();
        }
        return merchantContactResultResps;
    }

    @Override
    public List<MerchantContactResultResp> selectByAddressIds(Long tenantId, List<Long> storeIds, DefaultFlagEnum defaultFlagEnum) {
        MerchantContactQueryReq merchantContactQueryReq = new MerchantContactQueryReq();
        merchantContactQueryReq.setStoreIdList(storeIds);
        if (Objects.nonNull(defaultFlagEnum)) {
            merchantContactQueryReq.setDefaultFlag(defaultFlagEnum.getFlag());
        }
        merchantContactQueryReq.setTenantId(tenantId);
        List<MerchantContactResultResp> merchantContacts = userCenterMerchantContactFacade.getMerchantContacts(merchantContactQueryReq);
        if (Objects.isNull(merchantContacts)) {
            return Collections.emptyList();
        }
        return merchantContacts;
    }
}
