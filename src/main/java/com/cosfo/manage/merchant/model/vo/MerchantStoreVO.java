package com.cosfo.manage.merchant.model.vo;

import com.cosfo.manage.common.model.dto.PageQueryDTO;
import com.cosfo.manage.merchant.model.dto.MerchantContactDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAccountDTO;
import lombok.Data;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/23 10:51
 */
@Data
public class MerchantStoreVO extends PageQueryDTO {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 门店id列表
     */
    private List<Long> storeIds;

    /**
     * 租户id
     */
    private Long tenantId;


    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private List<Integer> typeList;


    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、关店
     */
    private Integer status;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 注册时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerTime;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 联系人
     */
    private List<MerchantContactDTO> contactList;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详情地址
     */
    private String address;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 审核标识 0、不通过 1、通过
     */
    private Integer auditFlag;

    /**
     * poi地址
     */
    private String poiNote;

    /**
     * 账期权限 0开启1关闭
     */
    private Integer billSwitch;

    /**
     * 在线支付
     */
    private Integer onlinePayment;

    /**
     * 1 开店 2 关店
     */
    private Integer operateStatus;

    /**
     * 手机号
     */
    private String accountPhone;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 分组Id
     */
    private Long groupId;

    /**
     * 分组Id
     */
    private List<Long> groupIds;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 是否为分组 -1
     */
    private Integer havingGroup;

    /**
     * 是否供应
     */
    private Integer supplyStatus;

    /**
     * 余额权限 0、关闭 1、开启
     */
    private Integer balanceAuthority;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 账号list
     */
    private List<MerchantStoreAccountDTO> accountList;

    /**
     * 分组ids
     */
    private List<Long> merchantStoreGroupIds;
    /**
     * 门店下单是否可下单
     * true = 可下单
     * false = 不可下单
     */
    private Boolean placeOrderEnableFlag;
    /**
     * 门店下单是否临期
     * true = 临期
     * false = 未临期
     */
    private Boolean placeOrderDeadlineFlag;
    /**
     * 门店下单有效期
     * 1=短期
     * 0=长期
     */
    private Integer placeOrderPermissionTimeLimited;
    /**
     * 门店地址，里面包含联系人信息
     */
    private List<MerchantAddressCommandReq> merchantAddressList;
    /**
     * 门店下单失效日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime placeOrderPermissionExpiryTime;

    /**
     * 线下支付权限1=开启;0=关闭
     */
    private Integer enableOfflinePayment;

    /**
     * 配送单展示价格 1=开启;0=关闭
     */
    private Integer enableDeliveryNotePrintPrice;

    private static final long serialVersionUID = 1L;

    /**
     * 账期/付款条件
     */
    private String billingPeriod;
    /**
     * 统一编号
     */
    private String uniqueNo;
    /**
     * 发票地址
     */
    private String invoiceAddress;

    /**
     * 邮箱
     */
    private String email;
}
