package com.cosfo.manage.merchant.model.vo.balance;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: fansongsong
 * @date: 2023-03-16  11:18
 * @Description:
 */
@Data
public class BalanceChangeRecordVO {

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 余额变动类型 0、预付 1、消费 2、消费退款
     */
    private Integer type;

    /**
     * 变动额度
     */
    private BigDecimal changeBalance;

    /**
     * 变动后额度
     */
    private BigDecimal afterChangeBalance;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作人手机号
     */
    private String operatorPhone;

    /**
     * 相关单号
     */
    private String associatedOrderNo;

    /**
     * 相关单号ID(type=1，订单ID type=2、售后单ID)
     */
    private Long associatedOrderId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 凭证
     */
    private String proof;

    /**
     * 记录变动时间
     */
    private LocalDateTime createTime;
}