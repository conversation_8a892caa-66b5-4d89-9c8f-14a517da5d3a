package com.cosfo.manage.merchant.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 描述: 门店注册信息
 *
 * @author: <EMAIL>
 * @创建时间: 2022/7/22
 */
@Data
public class MerchantStoreExcelDataDTO {

    /**
     * 自定义唯一门店编号（选填）
     */
    @ExcelProperty("自定义唯一门店编号（选填）")
    private String storeNo;

    /**
     * 门店名称
     */
    @ExcelProperty("客户名称")
    private String storeName;

    /**
     * 手机号
     */
    @ExcelProperty("店长手机号")
    private String phone;

    /**
     * 门店类型
     */
    @ExcelProperty("门店类型")
    private String type;

    /**
     * 账期权限
     */
    @ExcelProperty("账期权限")
    private String billPermissions;

    /**
     * 余额功能
     */
    @ExcelProperty("余额功能")
    private String balancePermissions;
    /**
     *  线下支付权限
     */
    @ExcelProperty("线下支付权限")
    private String offlinePaymentPermissions;

    /**
     * 省
     */
    @ExcelProperty("省份或直辖市")
    private String province;

    /**
     * 市
     */
    @ExcelProperty("市名")
    private String city;

    /**
     * 区
     */
    @ExcelProperty("县区")
    private String area;

    /**
     * 详细地址
     */
    @ExcelProperty("收货地址")
    private String address;

    /**
     * 地址备注
     */
    @ExcelProperty("门店备注（选填）")
    private String remark;

    /**
     * 联系人名称
     */
    @ExcelProperty("联络人名字")
    private String contactName;

    @ExcelProperty("联络人姓氏")
    private String contactName2;
    /**
     * 联系人手机号
     */
    @ExcelProperty("联络电话")
    private String contactPhone;

    /**
     * 门牌号
     */
    @ExcelProperty("门牌号（选填）")
    private String houseNumber;

    /**
     * 错误信息
     */
    @ExcelProperty("错误信息")
    private String errorMessage;

    /**
     * 状态
     */
    private String status;


    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 门店类型描述
     */
    private String typeDesc;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店下单是否可下单
     * true = 可下单
     * false = 不可下单
     */
    private Boolean placeOrderEnableFlag;

    /**
     * 门店下单是否可下单
     */
    private String placeOrderEnableFlagStr;

    /**
     * 门店下单失效日期
     */
    private LocalDateTime placeOrderPermissionExpiryTime;

    /**
     * 门店下单失效日期
     */
    private String placeOrderPermissionExpiryTimeStr;
}
