package com.cosfo.manage.merchant.model.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 描述:mch_store_account表的实体类
 * @version
 * @author:  Song
 * @创建时间: 2022-05-06
 */
@Data
public class MerchantStoreAccount {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 账号类型：0、店长 1、店员
     */
    private Integer type;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * open id
     */
    private String openId;

    /**
     * union id
     */
    private String unionId;

    /**
     * 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * @see com.cosfo.manage.common.context.MerchantStoreAccountDeleteFlagEnum
     */
    private Integer deleteFlag;

    /**
     * 上次登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 登录账号名称
     */
    private String username;

    private String email;

}
