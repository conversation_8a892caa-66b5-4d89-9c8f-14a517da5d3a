package com.cosfo.manage.merchant.model.dto.balance;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class MerchantStoreBalanceDTO {

    /**
     * 门店ID
     */
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    /**
     * 凭证
     */
    @NotNull(message = "凭证不能为空")
    private String proof;

    /**
     * 备注
     */
    private String remark;

    /**
     * 变动余额 （+ - 金额）
     */
    @DecimalMax(value = "100000000", message = "调整额度不能大于100000000")
    @NotNull(message = "调整额度不能为空")
    private BigDecimal changeBalance;
}
