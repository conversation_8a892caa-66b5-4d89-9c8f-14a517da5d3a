package com.cosfo.manage.merchant.model.dto;

import lombok.Data;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @date 2022/5/23 10:51
 */
@Data
public class MerchantStoreDTO {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;


    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、关店
     */
    private Integer status;

    /**
     * 门店状态描述
     */
    private String statusDesc;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 联系人Id
     */
    private Long contactId;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 收货地址
     */
    private String deliveryAddress;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详情地址
     */
    private String address;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 联系人
     */
    private List<MerchantContactDTO> contactList;

    /**
     * 账户名
     */
    private String accountName;

    /**
     * 0 店长 1 店员
     */
    private Integer accountType;

    /**
     * 账户注册时间
     */
    private LocalDateTime accountRegisterTime;

    /**
     * 账户审核时间
     */
    private LocalDateTime accountAuditTime;

    /**
     * 账户手机号
     */
    private String accountPhone;

    /**
     * 账户状态 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer accountStatus;

    /**
     * 0、否 1、是
     */
    private Integer defaultFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核标识 0、不通过 1、通过
     */
    private Integer auditFlag;

    /**
     * poi地址
     */
    private String poiNote;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 账期权限 0开启1关闭
     */
    private Integer billSwitch;

    /**
     * 在线支付 0开启1关闭
     */
    private Integer onlinePayment;

    /**
     * 余额权限 0、关闭 1、开启
     */
    private Integer balanceAuthority;

    /**
     * 余额
     */
    private BigDecimal balance;

    private String billPermissions;

    /**
     * 1 开店 2 关店
     */
    private Integer operateStatus;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 是否供应当前城市 0是1否
     */
    private Integer supplyStatus;

    /**
     * 门店分组Id
     */
    private Long groupId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 价格策略映射Id
     */
    private Long areaItemMappingId;

    /**
     * 账号集合
     */
    private List<MerchantStoreAccountDTO> accountList;

    /**
     * 账号分组名称
     */
    private String merchantStoreGroupName;

    /**
     * 三方编码映射
     * key=1 - 美团
     */
    private List<MerchantStoreCodeMapDTO> thirdCodeMap;
    /**
     * 门店下单有效期
     * 1=短期
     * 0=长期
     */
    private Integer placeOrderPermissionTimeLimited;
    /**
     * 门店下单是否可下单
     * true = 可下单
     * false = 不可下单
     */
    private Boolean placeOrderEnableFlag;
    /**
     * 门店下单失效日期
     */
    private LocalDateTime placeOrderPermissionExpiryTime;

    /**
     * 线下支付权限1=开启;0=关闭
     */
    private Integer enableOfflinePayment;

    /**
     * 配送单展示价格 1=开启;0=关闭
     */
    private Integer enableDeliveryNotePrintPrice;
    /**
     * 账期/付款条件
     */
    private String billingPeriod;
    /**
     * 统一编号
     */
    private String uniqueNo;
    /**
     * 发票地址
     */
    private String invoiceAddress;
    /**
     * 门店地址，里面包含联系人信息
     */
    private List<MerchantAddressCommandReq> merchantAddressList;
    /**
     * 联系人列表
     */
    private List<MerchantContactCommandReq> merchantContactList;
    /**
     * 邮箱
     */
    private String email;
}
