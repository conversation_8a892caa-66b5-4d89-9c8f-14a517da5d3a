package com.cosfo.manage.merchant.model.po;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * mch_store
 * <AUTHOR>
@Data
public class MerchantStore implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 账期开关 0开启 1关闭
     */
    private Integer billSwitch;

    /**
     * 在线支付0开启1关闭
     */
    private Integer onlinePayment;

    /**
     * 余额权限 0、关闭 1、开启
     */
    private Integer balanceAuthority;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 门店下单有效期
     * 1=短期
     * 0=长期
     */
    private Integer placeOrderPermissionTimeLimited;
    /**
     * 门店下单失效日期
     */
    private LocalDateTime placeOrderPermissionExpiryTime;

    /**
     * 线下支付权限1=开启;0=关闭
     */
    private Integer enableOfflinePayment;

    /**
     * 账期/付款条件
     */
    private String billingPeriod;
    /**
     * 统一编号
     */
    private String uniqueNo;
    /**
     * 发票地址
     */
    private String invoiceAddress;
    private static final long serialVersionUID = 1L;
}
