package com.cosfo.manage.downloadcenter.presaleorder;

import com.cosfo.manage.common.context.FileDownloadTypeEnum;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/7/9 下午6:37
 */
@Component
@Slf4j
public class PresaleOrderSetDeliveryDateImportHandler extends DownloadCenterImportDefaultHandler<PresaleOrderSetDeliveryDateImportDTO> {

    @Override
    public DownloadCenterEnum.RequestSource getSource() {
        return DownloadCenterEnum.RequestSource.SAAS;
    }

    @Override
    public Integer getBizType() {
        return FileDownloadTypeEnum.PRESALE_ORDER_SET_DELIVERY_DATE_IMPORT.getType();
    }

    @Override
    protected void dealExcelData(List<PresaleOrderSetDeliveryDateImportDTO> list, DownloadCenterDataMsg downloadCenterDataMsg) {
       log.info("处理预售订单设置发货日期导入数据, {}", list);
    }

    @Override
    protected File generateErrDataExcelFile(List<PresaleOrderSetDeliveryDateImportDTO> errorDataList) {
        log.info("生成错误数据文件, {}", errorDataList);
        return null;
    }
}
