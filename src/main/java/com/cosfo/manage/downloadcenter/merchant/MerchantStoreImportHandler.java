package com.cosfo.manage.downloadcenter.merchant;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.downloadcenter.item.MarketExcelDataDTO;
import com.cosfo.manage.facade.MarketFacade;
import com.cosfo.manage.facade.usercenter.UserCenterMerchantStoreFacade;
import com.cosfo.manage.market.converter.MarketItemConverter;
import com.cosfo.manage.market.model.dto.MarketAreaItemMappingInput;
import com.cosfo.manage.market.model.dto.MarketItemInput;
import com.cosfo.manage.market.model.dto.MarketQueryInput;
import com.cosfo.manage.market.model.dto.MarketSpuInput;
import com.cosfo.manage.market.model.vo.MarketSpuVO;
import com.cosfo.manage.market.service.MarketClassificationService;
import com.cosfo.manage.merchant.convert.MerchantStoreMapperConvert;
import com.cosfo.manage.merchant.model.dto.MerchantContactDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.manage.merchant.model.dto.MerchantStoreDTO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import net.xianmu.usercenter.client.common.page.PageQueryReq;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreCommandProvider;
import net.xianmu.usercenter.client.merchant.req.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 导入商品
 */
@Component
@Slf4j
public class MerchantStoreImportHandler extends DownloadCenterImportDefaultHandler<MerchantStoreExcelDataDTO> {

    @Resource
    private MerchantStoreCommandProvider merchantStoreCommandProvider;

    @Override
    public DownloadCenterEnum.RequestSource getSource() {
        return DownloadCenterEnum.RequestSource.SAAS;
    }

    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;

    @Override
    public Integer getBizType() {
        return FileDownloadTypeEnum.IMPORT_CUS_BASE_PRICE.getType();
    }
    @Override
    public void dealExcelData(List<MerchantStoreExcelDataDTO> list, DownloadCenterDataMsg downloadCenterDataMsg) {
        Long tenantId = downloadCenterDataMsg.getAuthUser().getTenantId();

        // 阶段一：前置数据清理
        cleanExcelData(list);
        validateExcelData(list);

        // 阶段二：数据解析和处理（包含重复数据检测）
        ParseResult parseResult = parseExcelDataToMerchantStores(list, tenantId);

        // 阶段三：门店信息创建
        createMerchantStores(parseResult.getMerchantStoreList(), parseResult.getStoreNoToRowsMapping(), list);

        log.info("MerchantStoreImportHandler - 处理完成，共处理 {} 条门店数据", parseResult.getMerchantStoreList().size());
    }

    /**
     * 阶段一：前置数据清理
     * 对Excel数据进行统一的清理和格式化处理
     */
    private void cleanExcelData(List<MerchantStoreExcelDataDTO> list) {
        for (MerchantStoreExcelDataDTO dto : list) {
            if (dto != null) {
                dto.setStoreNo(cleanValue(dto.getStoreNo()));
                dto.setStoreName(cleanValue(dto.getStoreName()));
                dto.setUniqueNo(cleanValue(dto.getUniqueNo()));
                dto.setBillingPeriod(cleanValue(dto.getBillingPeriod()));
                dto.setInvoiceAddress(cleanValue(dto.getInvoiceAddress()));
                dto.setName(cleanValue(dto.getName()));
                dto.setName2(cleanValue(dto.getName2()));
                dto.setEmail(cleanValue(dto.getEmail()));
                dto.setCity(cleanValue(dto.getCity()));
                dto.setArea(cleanValue(dto.getArea()));
                dto.setAddress(cleanValue(dto.getAddress()));

                // 专门处理电话号码
                dto.setPhone(preprocessPhoneNumber(dto.getPhone()));
            }
        }
    }

    /**
     * 清理字段值：去除空格、特殊字符等
     */
    private String cleanValue(String value) {
        if (value == null) {
            return "";
        }
        return value.replaceAll("\\s+", "").replaceAll("#", "").trim();
    }

    /**
     * 预处理电话号码：去除特殊字符、空格等
     */
    private String preprocessPhoneNumber(String phone) {
        if (phone == null) {
            return "";
        }

        // 去除 # 号
        if (phone.trim().equals("#")) {
            return "";
        }

        // 去除中划线和空格
        String cleanedPhone = phone.replaceAll("-", "")
                                  .replaceAll("\\s+#*", "")
                                  .trim();

        return cleanedPhone;
    }

    /**
     * 验证Excel数据的基本有效性
     */
    private void validateExcelData(List<MerchantStoreExcelDataDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException("没有找到匹配的客户");
        }
    }

    /**
     * 数据解析结果封装类
     */
    private static class ParseResult {
        private final List<MerchantStoreDTO> merchantStoreList;
        private final Map<String, List<Integer>> storeNoToRowsMapping;

        public ParseResult(List<MerchantStoreDTO> merchantStoreList, Map<String, List<Integer>> storeNoToRowsMapping) {
            this.merchantStoreList = merchantStoreList;
            this.storeNoToRowsMapping = storeNoToRowsMapping;
        }

        public List<MerchantStoreDTO> getMerchantStoreList() {
            return merchantStoreList;
        }

        public Map<String, List<Integer>> getStoreNoToRowsMapping() {
            return storeNoToRowsMapping;
        }
    }

    /**
     * 阶段二：数据解析和处理
     * 将Excel数据转换为门店业务对象，包含重复数据检测
     */
    private ParseResult parseExcelDataToMerchantStores(List<MerchantStoreExcelDataDTO> list, Long tenantId) {
        Map<String, MerchantStoreDTO> customerMap = new LinkedHashMap<>();
        // 用于跟踪主门店与Excel行号的映射关系
        Map<String, List<Integer>> storeNoToRowsMapping = new HashMap<>();
        // 用于检测重复数据
        Map<String, List<Integer>> duplicateMainStoreTracking = new HashMap<>();

        MerchantStoreDTO currentCustomer = null;
        String currentCustomerId = null;

        // 第一遍遍历：建立映射关系并检测重复数据
        for (int i = 0; i < list.size(); i++) {
            MerchantStoreExcelDataDTO dto = list.get(i);
            if (dto == null || StringUtils.isBlank(dto.getStoreNo())) {
                continue;
            }

            String storeNo = dto.getStoreNo().trim();
            if (StringUtils.isBlank(storeNo)) {
                continue;
            }

            String storeNoPrefix = storeNo.length() >= 4 ? storeNo.substring(0, 4) : storeNo;

            if (storeNoPrefix.startsWith("1291")) {
                // 检测重复的主门店
                if (duplicateMainStoreTracking.containsKey(storeNo)) {
                    // 标记之前的主门店及其子门店数据为被覆盖
                    List<Integer> previousRows = storeNoToRowsMapping.get(storeNo);
                    if (previousRows != null) {
                        markRowsAsOverridden(list, previousRows);
                    }

                    // 从映射中移除之前的数据
                    storeNoToRowsMapping.remove(storeNo);
                    customerMap.remove(storeNo);
                }

                // 记录当前主门店
                duplicateMainStoreTracking.put(storeNo, new ArrayList<>(Arrays.asList(i)));
                storeNoToRowsMapping.put(storeNo, new ArrayList<>(Arrays.asList(i)));

                // 处理主门店数据
                currentCustomer = processMainStore(dto, tenantId, storeNo);
                currentCustomerId = storeNo;
                customerMap.put(storeNo, currentCustomer);

            } else if (storeNoPrefix.startsWith("1299") && currentCustomerId != null && currentCustomer != null) {
                // 将子门店行号添加到对应主门店的映射中
                storeNoToRowsMapping.get(currentCustomerId).add(i);

                // 处理子门店数据
                processSubStore(dto, currentCustomer);
            }

            // 更新当前客户的基本信息
            if (currentCustomerId != null && currentCustomer != null) {
                currentCustomer.setAddress(StringUtils.isBlank(dto.getAddress()) ? "" : dto.getAddress().trim());
                currentCustomer.setEmail(StringUtils.isBlank(dto.getEmail()) ? "" : dto.getEmail().trim());
            }
        }

        List<MerchantStoreDTO> customerList = new ArrayList<>(customerMap.values());
        System.out.println(JSON.toJSONString(customerList));

        if (CollectionUtils.isEmpty(customerList)) {
            throw new BizException("没有找到匹配的客户");
        }

        return new ParseResult(customerList, storeNoToRowsMapping);
    }

    /**
     * 处理主门店数据（1291开头）
     */
    private MerchantStoreDTO processMainStore(MerchantStoreExcelDataDTO dto, Long tenantId, String storeNo) {
        MerchantStoreDTO merchantStore = new MerchantStoreDTO();
        merchantStore.setTenantId(tenantId);
        merchantStore.setStoreNo(storeNo);
        merchantStore.setStoreName(StringUtils.isBlank(dto.getStoreName()) ? "" : dto.getStoreName().trim());
        merchantStore.setUniqueNo(StringUtils.isBlank(dto.getUniqueNo()) ? "" : dto.getUniqueNo().trim());
        merchantStore.setBillingPeriod(StringUtils.isBlank(dto.getBillingPeriod()) ? "" : dto.getBillingPeriod().trim());
        merchantStore.setInvoiceAddress(StringUtils.isBlank(dto.getInvoiceAddress()) ? "" : dto.getInvoiceAddress().trim());

        // 初始化集合
        merchantStore.setContactList(new ArrayList<>());
        merchantStore.setMerchantAddressList(new ArrayList<>());
        merchantStore.setAccountList(new ArrayList<>());

        // 构建联系人信息
        buildContactInfo(dto, merchantStore, true);

        // 构建地址信息
        buildAddressInfo(dto, merchantStore, storeNo, true);

        // 构建账户信息
        buildAccountInfo(dto, merchantStore, storeNo);

        return merchantStore;
    }

    /**
     * 处理子门店数据（1299开头）
     */
    private void processSubStore(MerchantStoreExcelDataDTO dto, MerchantStoreDTO currentCustomer) {
        // 构建地址信息
        buildAddressInfo(dto, currentCustomer, dto.getStoreNo().trim(), false);

        // 构建联系人信息
        buildContactInfo(dto, currentCustomer, false);
    }

    /**
     * 构建联系人信息
     */
    private void buildContactInfo(MerchantStoreExcelDataDTO dto, MerchantStoreDTO merchantStore, boolean isDefault) {
        String contactName = buildContactName(dto.getName2(), dto.getName());
        String phoneValue = dto.getPhone();
        String email = StringUtils.isBlank(dto.getEmail()) ? "" : dto.getEmail().trim();

        // 如果是子门店且联系人姓名为空，使用上一个联系人的姓名
        if (!isDefault && StringUtils.isBlank(contactName)) {
            contactName = getLastValidContactName(merchantStore.getContactList());
        }

        if (!StringUtils.isBlank(phoneValue) && !phoneValue.equals("#")) {
            // 检查是否已存在相同的联系人
            if (!isDefault && contactExists(merchantStore.getContactList(), contactName, phoneValue)) {
                return;
            }

            MerchantContactDTO contact = new MerchantContactDTO();
            contact.setContactName(contactName);
            contact.setPhone(phoneValue);
            contact.setEmail(email);
            contact.setDefaultFlag(isDefault ? 1 : 0);
            merchantStore.getContactList().add(contact);
        }
    }

    /**
     * 构建联系人姓名
     */
    private String buildContactName(String name2, String name) {
        return (StringUtils.isBlank(name2) ? "" : name2.trim()) +
               (StringUtils.isBlank(name) ? "" : name.trim());
    }

    /**
     * 获取最后一个有效的联系人姓名
     */
    private String getLastValidContactName(List<MerchantContactDTO> contactList) {
        for (int i = contactList.size() - 1; i >= 0; i--) {
            MerchantContactDTO contact = contactList.get(i);
            if (!StringUtils.isBlank(contact.getContactName())) {
                return contact.getContactName();
            }
        }
        return "";
    }

    /**
     * 检查联系人是否已存在
     */
    private boolean contactExists(List<MerchantContactDTO> contactList, String contactName, String phoneValue) {
        return contactList.stream().anyMatch(contact ->
            contact != null &&
            Objects.equals(contact.getContactName(), contactName) &&
            Objects.equals(contact.getPhone(), phoneValue)
        );
    }

    /**
     * 构建地址信息
     */
    private void buildAddressInfo(MerchantStoreExcelDataDTO dto, MerchantStoreDTO merchantStore, String storeNo, boolean isDefault) {
        MerchantAddressCommandReq address = new MerchantAddressCommandReq();
        address.setCity(StringUtils.isBlank(dto.getCity()) ? "" : dto.getCity().trim());
        address.setArea(StringUtils.isBlank(dto.getArea()) ? "" : dto.getArea().trim());
        address.setAddress(StringUtils.isBlank(dto.getAddress()) ? "" : dto.getAddress().trim());
        address.setStoreNo(storeNo);
        address.setDefaultFlag(isDefault ? 1 : 0);

        List<MerchantAddressCommandReq> addressList = merchantStore.getMerchantAddressList();
        if (addressList == null) {
            addressList = new ArrayList<>();
            merchantStore.setMerchantAddressList(addressList);
        }
        addressList.add(address);
    }

    /**
     * 构建账户信息
     */
    private void buildAccountInfo(MerchantStoreExcelDataDTO dto, MerchantStoreDTO merchantStore, String storeNo) {
        String phoneValue = dto.getPhone();

        if (!StringUtils.isBlank(phoneValue) && !phoneValue.equals("#")) {
            MerchantStoreAccountDTO account = new MerchantStoreAccountDTO();
            account.setUsername(storeNo);
            account.setAccountName(buildContactName(dto.getName2(), dto.getName()));
            account.setEmail(dto.getEmail());
            account.setType(0);

            // 设置电话号码：优先使用有效的10位数字电话号码
            String validPhone = findFirstValidPhone(merchantStore.getContactList());
            if (StringUtils.isNotEmpty(validPhone)) {
                account.setPhone(validPhone.trim());
            } else {
                account.setPhone(phoneValue);
            }

            merchantStore.getAccountList().add(account);
        }
    }

    /**
     * 查找第一个有效的电话号码（10位数字）
     */
    private String findFirstValidPhone(List<MerchantContactDTO> contactList) {
        return contactList.stream()
            .map(MerchantContactDTO::getPhone)
            .filter(phone -> StringUtils.isNotEmpty(phone) && Pattern.matches("\\d{10}", phone))
            .findFirst()
            .orElse("");
    }

    /**
     * 标记数据行为被覆盖
     */
    private void markRowsAsOverridden(List<MerchantStoreExcelDataDTO> list, List<Integer> rowIndices) {
        for (Integer rowIndex : rowIndices) {
            if (rowIndex < list.size()) {
                list.get(rowIndex).setErrorMsg("被覆盖的重复数据行");
            }
        }
    }

    /**
     * 阶段三：门店信息创建
     * 调用facade创建门店信息
     */
    private void createMerchantStores(List<MerchantStoreDTO> merchantStoreList,
                                     Map<String, List<Integer>> storeNoToRowsMapping,
                                     List<MerchantStoreExcelDataDTO> originalList) {
        merchantStoreList.forEach(merchantStoreDTO -> {
            String storeNo = merchantStoreDTO.getStoreNo();
            try {
                MerchantStoreDomainCommandReq merchantStoreDomainCommandReq = buildMerchantStoreDomainCommandReq(null, merchantStoreDTO);
                userCenterMerchantStoreFacade.createMerchantStoreInfo(merchantStoreDomainCommandReq);
            } catch (Exception e) {
                log.error("创建商户门店信息失败，门店编号: {}", storeNo, e);
                buildErrorMessage(originalList, e.getMessage(), storeNo, storeNoToRowsMapping);
            }
        });
    }

    /**
     * 构建错误信息，基于门店编号和行号映射关系
     */
    private void buildErrorMessage(List<MerchantStoreExcelDataDTO> list, String errorMessage,
                                  String storeNo, Map<String, List<Integer>> storeNoToRowsMapping) {
        try {
            List<Integer> relatedRows = storeNoToRowsMapping.get(storeNo);
            if (relatedRows != null) {
                for (Integer rowIndex : relatedRows) {
                    if (rowIndex < list.size()) {
                        MerchantStoreExcelDataDTO dto = list.get(rowIndex);
                        // 只有当错误信息为空时才设置，避免覆盖已有的错误信息（如"被覆盖的重复数据行"）
                        if (StringUtils.isBlank(dto.getErrorMsg())) {
                            dto.setErrorMsg(errorMessage);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error("构建错误信息失败，门店编号: {}", storeNo, ex);
        }
    }


    private void validExcelPropertyEmpty(MerchantStoreExcelDataDTO excelDataDTO) {
        // 验证每个字段
        if (StringUtils.isBlank(excelDataDTO.getStoreNo())) {
            throw new BizException("客戶代號不可爲空");
        }


    }
    private MerchantStoreDomainCommandReq buildMerchantStoreDomainCommandReq(Long regionalId, MerchantStoreDTO merchantStoreDTO) {
        MerchantStoreDomainCommandReq merchantStoreDomainCommandReq = new MerchantStoreDomainCommandReq();
        MerchantStoreCommandReq merchantStoreCommandReq = MerchantStoreMapperConvert.INSTANCE.dtoToStoreCommandReq(merchantStoreDTO);
        merchantStoreCommandReq.setAuditTime(LocalDateTime.now());
        merchantStoreCommandReq.setRegionalId(regionalId);
        merchantStoreDomainCommandReq.setMerchantStore(merchantStoreCommandReq);
        merchantStoreDomainCommandReq.setGroupId(merchantStoreDTO.getGroupId());

        List<MerchantAddressCommandReq> addressList = new ArrayList<>();
        if (merchantStoreDTO.getMerchantAddressList() != null && !merchantStoreDTO.getMerchantAddressList().isEmpty()) {
            for (MerchantAddressCommandReq address : merchantStoreDTO.getMerchantAddressList()) {
                address.setTenantId(merchantStoreDTO.getTenantId());
                String storeNo = address.getStoreNo();
                if (storeNo != null && storeNo.startsWith("1291")) {
                    address.setDefaultFlag(DefaultTypeEnum.DEFAULT.getCode());
                } else {
                    address.setDefaultFlag(DefaultTypeEnum.NO_DEFAULT.getCode());
                }
                address.setStatus(AuditFlagEnum.AUDIT_SUCCESS.getStatus());
                addressList.add(address);
            }
        }

        List<MerchantContactCommandReq> merchantContactCommandReqList = new ArrayList<>();
        List<MerchantContactDTO> contactList = merchantStoreDTO.getContactList();
        if (contactList != null) {
            for (MerchantContactDTO contact : contactList) {
                MerchantContactCommandReq merchantContact = new MerchantContactCommandReq();
                merchantContact.setTenantId(merchantStoreDTO.getTenantId());
                merchantContact.setName(contact.getContactName());
                merchantContact.setPhone(contact.getPhone());
                merchantContact.setEmail(contact.getEmail());
                merchantContact.setDefaultFlag(contact.getDefaultFlag());
                merchantContactCommandReqList.add(merchantContact);
            }
        }
        merchantStoreDomainCommandReq.setMerchantContactList(merchantContactCommandReqList);
        if (!addressList.isEmpty() && !merchantContactCommandReqList.isEmpty()) {
            addressList.get(0).setMerchantContactList(merchantContactCommandReqList);
        }

        merchantStoreDomainCommandReq.setMerchantAddressList(addressList);
        merchantStoreDomainCommandReq.setMerchantContactList(merchantContactCommandReqList);

        List<MerchantStoreAccountCommandReq> merchantStoreAccountList = new ArrayList<>();
        List<MerchantStoreAccountDTO> accountList = merchantStoreDTO.getAccountList();

        String firstValidPhone = "";

        for (MerchantContactDTO contact : merchantStoreDTO.getContactList()) {
            String contactPhone = contact.getPhone();
            if (StringUtils.isNotEmpty(contactPhone) && Pattern.matches("\\d{10}", contactPhone)) {
                firstValidPhone = contactPhone;
                break;
            }
        }

        if (accountList != null) {

            for (MerchantStoreAccountDTO merchantStoreAccountDTO : accountList) {
                MerchantStoreAccountCommandReq merchantStoreAccount = new MerchantStoreAccountCommandReq();
                merchantStoreAccount.setTenantId(merchantStoreDTO.getTenantId());
                merchantStoreAccount.setAccountName(merchantStoreAccountDTO.getAccountName());
                merchantStoreAccount.setUsername(merchantStoreAccountDTO.getUsername());

                if (!firstValidPhone.isEmpty()) {
                    merchantStoreAccount.setPhone(firstValidPhone);
                } else {
                    merchantStoreAccount.setPhone(merchantStoreAccountDTO.getPhone());
                }
                merchantStoreAccount.setType(merchantStoreAccountDTO.getType());
                merchantStoreAccount.setRegisterTime(LocalDateTime.now());
                merchantStoreAccount.setAuditTime(LocalDateTime.now());
                merchantStoreAccount.setStatus(MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode());
                merchantStoreAccount.setEmail(merchantStoreAccountDTO.getEmail());
                merchantStoreAccountList.add(merchantStoreAccount);
            }
        }
        merchantStoreDomainCommandReq.setMerchantStoreAccountList(merchantStoreAccountList);

        return merchantStoreDomainCommandReq;
    }

    @Override
    protected File generateErrDataExcelFile(List<MerchantStoreExcelDataDTO> errorDataList) {
        log.info("生成错误数据文件, {}", errorDataList);
        File file = new File(UUID.randomUUID().toString() + ".xlsx");
        EasyExcel.write(file, MerchantStoreExcelDataDTO.class).sheet().registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).doWrite(errorDataList);
        return file;
    }
}
