package com.cosfo.manage.downloadcenter.item;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import net.xianmu.download.support.dto.ImportExcelBaseDTO;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.lang.reflect.Field;
import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/23
 */
@Data
public class MarketExcelDataDTO extends ImportExcelBaseDTO {
    /**
     * 租户id
     */
    @ExcelIgnore
    private Long tenantId;

    @ExcelProperty("商品名稱（必填）")
    private String title;
    @ExcelIgnore
    private String subTitle;
    @ExcelIgnore
    private String firstClassificationName;
    @ExcelIgnore
    private String secondClassificationName;
    @ExcelIgnore
    private String firstCategoryName;

    @ExcelProperty("商品分组（必填）")
    private String classificationName;

//    @Pattern(regexp = "^[a-zA-Z0-9]{1,30}+$", message = "SAP-SKU编码不符合格式")
    @ExcelProperty("商品料號（18碼）（必填）")
    private String sapSkuCode;

//    @Pattern(regexp = "^[a-zA-Z0-9]{1,30}+$", message = "物料编码不符合格式")
    @ExcelProperty("商品料號（10碼）（必填）")
    private String sapMaterialCode;

//    @Pattern(regexp = "^{1,150}+$", message = "商品描述不符合格式")
    @ExcelProperty("詳情描述")
    private String descriptionString;

    @ExcelProperty("品牌")
    private String brandName;

    @ExcelProperty("規格")
    private String specificationUnit;

    @Min(value = 1, message = "包裝數量必須大於等於1")
    @NotNull(message = "包裝數量必須大於等於1")
    @ExcelProperty("包裝數量 （必填）")
    private Integer packingQuantity;


    @Min(value = 1, message = "最小起订量必須大於等於1")
    @NotNull(message = "最小起订量必須大於等於1")
    @ExcelProperty("最小起订量（必填）")
    private Integer miniOrderQuantity;
    @ExcelIgnore
    private String secondCategoryName;
    @ExcelIgnore
    private String thirdCategoryName;
    @ExcelIgnore
    private String operationMode;
    @ExcelIgnore
    private Long skuId;
    @ExcelIgnore
    private Long supplierId;
    @ExcelIgnore
    private String supplierName;
    @ExcelIgnore
    private BigDecimal noGoodsSupplyPrice;
    @ExcelIgnore
    private String itemCode;
    @ExcelIgnore
    private String specificationType;
    @ExcelIgnore
    private String specification;
    @ExcelIgnore
    private String onSale;
    @ExcelIgnore
    private Integer stock;
    @ExcelIgnore
    private String buyMultipleSwitch;
    @ExcelIgnore
    private Integer buyMultiple;
    @ExcelIgnore
    private String saleLimitRuleName;
    @ExcelIgnore
    private Integer saleLimitQuantity;
    @ExcelIgnore
    private String itemSaleModeName;
    @ExcelIgnore
    private String presaleSwitch;
    @ExcelIgnore
    private String priceTargetType;
    @ExcelIgnore
    private String priceTargets;
    @ExcelIgnore
    private String priceStrategy;
    @ExcelIgnore
    private BigDecimal price;
    @ExcelIgnore
    private String unfairPriceStrategyDefaultFlag;
    @ExcelIgnore
    private String unfairPriceStrategy;
    @ExcelIgnore
    private String storeInventoryControlFlag;
    @ExcelIgnore
    private String storeCostUnit;
    @ExcelIgnore
    private String storeInventoryUnit;
    @ExcelIgnore
    private String storeOrderingUnit;
    @ExcelIgnore
    private BigDecimal storeInventoryCostUnitMultiple;
    @ExcelIgnore
    private BigDecimal storeOrderingInventoryUnitMultiple;
    @ExcelIgnore
    private BigDecimal standardUnitPrice;
    @ExcelIgnore
    private String afterSaleUnit;

    @ExcelIgnore
    private Integer maxAfterSaleAmount;

    public static void main(String[] args) {
        Field[] fields = MarketExcelDataDTO.class.getDeclaredFields();
        for (Field field : fields) {
            ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (annotation != null) {
                System.out.println(annotation.value()[0]);
            }
        }
    }
}
