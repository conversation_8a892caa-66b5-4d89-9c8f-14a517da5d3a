package com.cosfo.manage.downloadcenter.item;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.facade.MarketFacade;
import com.cosfo.manage.facade.MarketItemFacade;
import com.cosfo.manage.market.converter.MarketItemConverter;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.model.po.MarketItem;
import com.cosfo.manage.market.model.vo.MarketSpuVO;
import com.cosfo.manage.market.service.MarketClassificationService;
import com.cosfo.manage.merchant.service.MerchantStoreGroupService;
import com.cosfo.manage.merchant.service.MerchantStoreService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 导入商品
 */
@Component
@Slf4j
public class ItemImportHandler extends DownloadCenterImportDefaultHandler<MarketExcelDataDTO> {

    @Resource
    private MarketClassificationService marketClassificationService;
    @Resource
    private MarketFacade marketFacade;
    @Override
    public DownloadCenterEnum.RequestSource getSource() {
        return DownloadCenterEnum.RequestSource.SAAS;
    }

    @Override
    public Integer getBizType() {
        return FileDownloadTypeEnum.ITEM_IMPORT.getType();
    }

    @Override
    public void dealExcelData(List<MarketExcelDataDTO> list, DownloadCenterDataMsg downloadCenterDataMsg) {
        Long tenantId = downloadCenterDataMsg.getAuthUser ().getTenantId ();

        //前台分类map《2级分类name/1级分类name，2级别分类id》
        Map<String, Long> classificationMap = marketClassificationService.getClassificationMap (tenantId);

        log.info ("ItemImportHandler - list={}",list);
        for (MarketExcelDataDTO excelDataDTO : list) {
            try {
                validExcelPropertyEmpty (excelDataDTO);
                MarketQueryInput marketQueryInput = new MarketQueryInput();
                marketQueryInput.setSapSkuCode(excelDataDTO.getSapSkuCode ());
                PageInfo<MarketSpuVO> marketSpuVOPageInfo = marketFacade.queryMarketListPage(marketQueryInput);
                //根据某个编码查出来的值
                List<MarketSpuVO> list1 = marketSpuVOPageInfo.getList();
                MarketSpuInput input = buildMarketSpuInput (tenantId, excelDataDTO, classificationMap);
                if(CollectionUtils.isEmpty(list1)){
                    input.getMarketItemInput().setOnSale (com.cofso.item.client.enums.OnSaleTypeEnum.SOLD_OUT.getCode ());
                    marketFacade.addMarket (input);
                }else{
                    input.setId(list1.get(0).getId());
                    input.getMarketItemInput().setId(list1.get(0).getMarketItemVOList().get(0).getId());
                    marketFacade.updateMarket(input);
                }
            } catch (Exception e){
                log.error ("ItemImportHandler - error",e);
                excelDataDTO.setErrorMsg (e.getMessage ());
            }
        }
    }

    private MarketSpuInput buildMarketSpuInput(Long tenantId,MarketExcelDataDTO excelDataDTO, Map<String, Long> classificationMap) {
        MarketSpuInput marketSpuInput = new MarketSpuInput();
        marketSpuInput.setExcelId (1);
        marketSpuInput.setTenantId (tenantId);
        marketSpuInput.setTitle (excelDataDTO.getTitle ());
        marketSpuInput.setBrandName(excelDataDTO.getBrandName ());
        marketSpuInput.setDescriptionString(excelDataDTO.getDescriptionString ());
        MarketItemInput marketItemInput = MarketItemConverter.convertToMarketItemInput4ExcelDataDTO (excelDataDTO,tenantId);
        Long classifcationId = classificationMap.get (excelDataDTO.getClassificationName());
        if(classifcationId == null){
            throw new BizException ("商品分類不存在");
        }else{
            marketSpuInput.setClassificationId (classifcationId);
        }

        MarketAreaItemMappingInput price  = new MarketAreaItemMappingInput();
//        price.setPriceType (1);
//        price.setType (2);
//        price.setMappingNumber (new BigDecimal ("9999"));
        marketItemInput.setDefaultPrice (price);
//        marketItemInput.setBrandName(excelDataDTO.getBrandName ());
//        marketItemInput.setSpecificationUnit(excelDataDTO.getSpecificationUnit ());
//        marketItemInput.setDescriptionString(excelDataDTO.getDescriptionString ());
        marketItemInput.setAfterSaleUnit ("pcs");

        marketItemInput.setMaxAfterSaleAmount (1);

        marketItemInput.setGoodsType (GoodsTypeEnum.NO_GOOD_TYPE.getCode ());

        marketItemInput.setSaleLimitRule(0);

        marketItemInput.setBuyMultipleSwitch(false);

        marketItemInput.setPresaleSwitch(0);

        ItemSaleModeExcelEnum itemSaleModeEnum =  ItemSaleModeExcelEnum.getByDesc(ItemSaleModeExcelEnum.NORMAL_SALE.getDesc ());
        marketItemInput.setItemSaleMode(itemSaleModeEnum.getType());

        marketSpuInput.setMarketItemInput (marketItemInput);
        return marketSpuInput;
    }


    private void validExcelPropertyEmpty(MarketExcelDataDTO excelDataDTO) {
        // 验证每个字段
        if (StringUtils.isBlank (excelDataDTO.getTitle())) {
            throw new BizException("商品名稱不能為空白");
        }
        if(StringUtils.isBlank(excelDataDTO.getClassificationName())){
            throw new BizException("商品分類不能為空白");
        }
        if(StringUtils.isBlank(excelDataDTO.getSapSkuCode())){
            throw new BizException("商品料號（18碼）不能為空白");
        }
        if(StringUtils.isBlank(excelDataDTO.getSapMaterialCode())){
            throw new BizException("商品料號（10碼）不能為空白");
        }

        if(excelDataDTO.getPackingQuantity()==null){
            throw new BizException("包裝數量不能為空白");
        }

        if (excelDataDTO.getMiniOrderQuantity() == null) {
            throw new BizException("最小起訂量不能為空白");
        }

    }


    @Override
    protected File generateErrDataExcelFile(List<MarketExcelDataDTO> errorDataList) {
        log.info("生成错误数据文件, {}", errorDataList);
        File file = new File(UUID.randomUUID().toString() + ".xlsx");
        EasyExcel.write(file, MarketExcelDataDTO.class).sheet().registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).doWrite(errorDataList);
        return file;
    }
}
