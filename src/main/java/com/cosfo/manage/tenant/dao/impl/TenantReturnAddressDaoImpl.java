package com.cosfo.manage.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.manage.common.context.DefaultFlagEnum;
import com.cosfo.manage.common.context.DeleteFlagEnum;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.order.model.vo.ReturnAddressAddInput;
import com.cosfo.manage.order.model.vo.ReturnAddressUpdateInput;
import com.cosfo.manage.order.model.vo.ReturnAddressVO;
import com.cosfo.manage.tenant.dao.TenantReturnAddressDao;
import com.cosfo.manage.tenant.mapper.TenantReturnAddressMapper;
import com.cosfo.manage.tenant.model.po.TenantReturnAddress;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 租户售后退货地址信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-27
 */
@Service
public class TenantReturnAddressDaoImpl extends ServiceImpl<TenantReturnAddressMapper, TenantReturnAddress> implements TenantReturnAddressDao {

    @Override
    public List<ReturnAddressVO> queryByTenantId(Long tenantId) {
        LambdaQueryWrapper<TenantReturnAddress> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(TenantReturnAddress::getTenantId, tenantId);
        lambdaQueryWrapper.eq(TenantReturnAddress::getDeleteFlag, DeleteFlagEnum.NORMAL.getFlag());
        List<TenantReturnAddress> list = list(lambdaQueryWrapper);

        return list.stream().map(e -> {
            ReturnAddressVO vo = new ReturnAddressVO();
            BeanUtils.copyProperties(e, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public ReturnAddressVO getByPrimaryKey(Long id) {
        if(id == null){
            return null;
        }
        TenantReturnAddress tenantReturnAddress = getBaseMapper().selectById(id);
        if(tenantReturnAddress == null){
            return null;
        }
        ReturnAddressVO vo = new ReturnAddressVO();
        BeanUtils.copyProperties(tenantReturnAddress, vo);
        return vo;
    }

    @Override
    public ReturnAddressVO getRecentlyUsedAddress(Long tenantId) {
        LambdaQueryWrapper<TenantReturnAddress> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(TenantReturnAddress::getTenantId, tenantId);
        lambdaQueryWrapper.eq(TenantReturnAddress::getDeleteFlag, DeleteFlagEnum.NORMAL.getFlag());
        lambdaQueryWrapper.orderByDesc(TenantReturnAddress::getUpdateTime);
        lambdaQueryWrapper.last("limit 1");
        TenantReturnAddress tenantReturnAddress = getOne(lambdaQueryWrapper);
        if(tenantReturnAddress == null){
            return null;
        }
        ReturnAddressVO vo = new ReturnAddressVO();
        BeanUtils.copyProperties(tenantReturnAddress, vo);
        return vo;
    }

    @Override
    public void useAddress(Long id) {
        LambdaUpdateWrapper<TenantReturnAddress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TenantReturnAddress::getId, id);
        updateWrapper.set(TenantReturnAddress::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
    }

    @Override
    public Long insert(ReturnAddressAddInput returnAddressAddInput, Long tenantId) {
        TenantReturnAddress obj = new TenantReturnAddress();
        BeanUtils.copyProperties(returnAddressAddInput, obj);
        obj.setTenantId(tenantId);
        obj.setDefaultFlag(DefaultFlagEnum.FALSE.getFlag());
        obj.setDeleteFlag(DeleteFlagEnum.NORMAL.getFlag());
        obj.setUpdateTime(LocalDateTime.now());
        save(obj);
        return obj.getId();
    }

    @Override
    public void update(ReturnAddressUpdateInput input) {
        Long id = input.getId();
        if(id == null){
            throw new BizException("id不能空白");
        }
        LambdaUpdateWrapper<TenantReturnAddress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TenantReturnAddress::getId, id);
        updateWrapper.set(!StringUtils.isEmpty(input.getProvince()), TenantReturnAddress::getProvince, input.getProvince());
        updateWrapper.set(!StringUtils.isEmpty(input.getCity()), TenantReturnAddress::getCity, input.getCity());
        updateWrapper.set(!StringUtils.isEmpty(input.getArea()), TenantReturnAddress::getArea, input.getArea());
        updateWrapper.set(!StringUtils.isEmpty(input.getAddress()), TenantReturnAddress::getAddress, input.getAddress());
        updateWrapper.set(!StringUtils.isEmpty(input.getHouseNo()), TenantReturnAddress::getHouseNo, input.getHouseNo());
        updateWrapper.set(!StringUtils.isEmpty(input.getContactName()), TenantReturnAddress::getContactName, input.getContactName());
        updateWrapper.set(!StringUtils.isEmpty(input.getContactPhone()), TenantReturnAddress::getContactPhone, input.getContactPhone());

        boolean flag = update(updateWrapper);
        if(!flag){
            throw new BizException("更新失敗");
        }
    }

    @Override
    public void delete(Long id) {
        LambdaUpdateWrapper<TenantReturnAddress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TenantReturnAddress::getId, id);
        updateWrapper.set(TenantReturnAddress::getDeleteFlag, DeleteFlagEnum.DELETED.getFlag());

        boolean flag = update(updateWrapper);
        if(!flag){
            throw new BizException("删除失败");
        }
    }

    @Override
    public List<TenantReturnAddress> queryByIds(List<Long> ids) {
        LambdaQueryWrapper<TenantReturnAddress> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TenantReturnAddress::getDeleteFlag, DeleteFlagEnum.NORMAL.getFlag());
        lambdaQueryWrapper.in(TenantReturnAddress::getId, ids);
        return list(lambdaQueryWrapper);
    }
}
