package com.cosfo.manage.tenant.model.dto;

import com.cosfo.manage.tenant.model.vo.TenantAccountBussinessMsgConfigVO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 账号表
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@Data
public class TenantAccountDTO {

    private Long id;

    /**
     * 品牌方Id
     */
    private Long tenantId;

    /**
     * 手机号
     */
    //@Length(max = 11, min = 5,message = "手机号错误")
    private String phone;

    /**
     * 登陆密码
     */
    private String loginPassword;
    /**
     * 用户名称
     */
    private String nickname;

    /**
     * 头像
     */
    private String profilePicture;

    /**
     * 角色Id
     */
    private List<Long> roleIds;

    /**
     * 0有效1失效
     */
    private Integer status;

    /**
     * authBaseId
     */
    private Long authBaseId;

    /**
     * 操作时间
     */
    private LocalDateTime operatorTime;

    /**
     * 操作手机号
     */
    private String operatorPhone;

    /**
     * 供应商ids
     */
    private List<Long> supplierIds;

    /**
     * 推送内容
     */
    private List<TenantAccountBussinessMsgConfigDTO> configVOS;

    /**
     * 操作人
     */
    private String updater;


    /**
     * email
     */
    @Email(message = "邮箱格式不正确")
    @NotBlank
    private String email;
}
