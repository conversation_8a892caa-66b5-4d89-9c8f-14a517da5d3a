package com.cosfo.manage.tenant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.TenantConfigEnum;
import com.cosfo.manage.common.converter.AuthRoleMapper;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import com.cosfo.manage.facade.AuthPurviewFacade;
import com.cosfo.manage.facade.AuthUserFacade;
import com.cosfo.manage.tenant.model.dto.RoleDTO;
import com.cosfo.manage.tenant.model.dto.RoleQueryDTO;
import com.cosfo.manage.tenant.model.po.TenantCommonConfig;
import com.cosfo.manage.tenant.model.vo.RoleVO;
import com.cosfo.manage.tenant.service.AuthRoleService;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthUserRoleDto;
import net.xianmu.common.exception.BizException;
import net.xianmu.log.config.BizLogRecordContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.cosfo.manage.common.context.TenantConfigEnum.TenantConfig.SUPPLIER_DISTRIBUTOR_ROLE_ID;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthRoleServiceImpl implements AuthRoleService {

    @Resource
    private AuthUserFacade authUserFacade;
    @Resource
    private AuthPurviewFacade authPurviewFacade;
    @Resource
    private TenantCommonConfigService tenantCommonConfigService;

    @Override
    public List<Long> addAuthRole(RoleDTO roleDTO) {
        Long roleId = authUserFacade.addRole(roleDTO, null);
        // 增加日志上下文
        buildAddRoleContext(roleDTO, roleId);

        return roleDTO.getMenuPurviewIds();
    }

    private void buildAddRoleContext(RoleDTO roleDTO, Long roleId) {
        try {
            BizLogRecordContext.put("roleId", roleId);
            BizLogRecordContext.put("tenantId", UserLoginContextUtils.getTenantId());
            BizLogRecordContext.put("roleName", roleDTO.getRoleName());
            Map<String, Object> content = new HashMap<>();
            content.put("roleName", BizLogRecordContext.get("roleName"));
            BizLogRecordContext.put("content", content);
        } catch (Exception e) {
            log.error("获取日志上下文失败！", e);
        }
    }

    @Override
    public List<Long> updateAuthRole(RoleDTO roleDTO) {
        // 增加日志上下文
        saveUpdateRoleContext(roleDTO);

        authUserFacade.updateRole(roleDTO);
        return roleDTO.getMenuPurviewIds();
    }

    private void saveUpdateRoleContext(RoleDTO roleDTO) {
        try {
            Long tenantId = UserLoginContextUtils.getTenantId();
            BizLogRecordContext.put("roleId", roleDTO.getId());
            BizLogRecordContext.put("tenantId", tenantId);

            RoleDTO oldRole = authUserFacade.getRole(Long.valueOf(roleDTO.getId()));
            if (!CollectionUtils.isEmpty(oldRole.getMenuPurviewIds())) {
                BizLogRecordContext.put("oldMenuPurview", authPurviewFacade.listMenuNameTile(tenantId, oldRole.getMenuPurviewIds()));
            }
            if (!CollectionUtils.isEmpty(roleDTO.getMenuPurviewIds())) {
                BizLogRecordContext.put("newMenuPurview", authPurviewFacade.listMenuNameTile(tenantId, roleDTO.getMenuPurviewIds()));
            }

            Map<String, Object> content = new HashMap<>();
            content.put("oldRole", oldRole);
            content.put("newRole", roleDTO);
            content.put("oldMenuPurview", BizLogRecordContext.get("oldMenuPurview"));
            content.put("newMenuPurview", BizLogRecordContext.get("newMenuPurview"));
            BizLogRecordContext.put("content", content);
        } catch (Exception e) {
            log.error("获取日志上下文失败！", e);
        }
    }

    @Override
    public Boolean delAuthRole(RoleDTO roleDTO) {
        RoleDTO oldRole = authUserFacade.getRole(Long.valueOf(roleDTO.getId()));
        BizLogRecordContext.put("roleName", oldRole.getRoleName());

        Boolean deleteResult = authUserFacade.delRole(Long.valueOf(roleDTO.getId()));
        // 增加日志上下文
        buildLogContext(roleDTO, deleteResult);
        return deleteResult;
    }

    private void buildLogContext(RoleDTO roleDTO, Boolean deleteResult) {
        try {
            BizLogRecordContext.put("roleId", roleDTO.getId());
            BizLogRecordContext.put("tenantId", UserLoginContextUtils.getTenantId());
            Map<String, Object> content = new HashMap<>();
            content.put("deleteResult", deleteResult);
            content.put("roleName", BizLogRecordContext.get("roleName"));
            BizLogRecordContext.put("content", content);
        } catch (Exception e) {
            log.error("获取日志上下文失败！", e);
        }
    }

    @Override
    public RoleVO getAuthRole(Long roleId, LoginContextInfoDTO loginContextInfoDTO) {
        RoleDTO role = authUserFacade.getRole(roleId);
        if (role == null) {
            throw new BizException("無登錄此角色權限");
        }
        if (!Objects.equals(role.getTenantId(), loginContextInfoDTO.getTenantId())) {
            log.warn("当前角色和租户信息不匹配, role={}, loginInfo={}", JSON.toJSONString(role), JSON.toJSONString(loginContextInfoDTO));
            throw new BizException("無登錄此角色權限");
        }
        return AuthRoleMapper.INSTANCE.roleDTOToRoleVO(role);
    }

    @Override
    public Boolean checkIsIncludeRole(Long accountId, Long roleId) {
        List<AuthUserRoleDto> userRoles = authUserFacade.getUserRoleByUserIds(Arrays.asList(accountId));
        if (CollectionUtils.isEmpty(userRoles)) {
            return Boolean.FALSE;
        }
        AuthUserRoleDto authUserRoleDto = userRoles.get(NumberConstants.ZERO);
        List<AuthRole> roles = authUserRoleDto.getRoles();
        if (CollectionUtils.isEmpty(roles)) {
            return Boolean.FALSE;
        }
        return roles.stream().anyMatch(el -> Objects.equals(el.getId(), roleId));
    }

    @Override
    public Boolean isSupplierRole(Long tenantId, Long authUserId) {
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigService.selectByTenantIdAndConfigKey(tenantId, SUPPLIER_DISTRIBUTOR_ROLE_ID.getConfigKey());
        Boolean isSupplierDistributor = Boolean.FALSE;
        if (tenantCommonConfig != null) {
            isSupplierDistributor = checkIsIncludeRole(authUserId, Long.valueOf(tenantCommonConfig.getConfigValue()));
        }
        return isSupplierDistributor;
    }

    @Override
    public PageInfo<RoleVO> queryRolePage(RoleQueryDTO roleQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        PageInfo<RoleDTO> pageInfo = authUserFacade.queryRoleByPage(roleQueryDTO);
        List<RoleDTO> roleDTOList = pageInfo.getList();
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigService.selectByTenantIdAndConfigKey(loginContextInfoDTO.getTenantId(), TenantConfigEnum.TenantConfig.SUPPLIER_DISTRIBUTOR_ROLE_ID.getConfigKey());
        if (tenantCommonConfig != null) {
            String supplierDistributorRoleId = tenantCommonConfig.getConfigValue();
            roleDTOList.stream().filter(el -> Objects.equals(el.getId(), Integer.valueOf(supplierDistributorRoleId))).forEach(el -> el.setSupplierDistributorFlag(Boolean.TRUE));
        }
        return AuthRoleMapper.INSTANCE.roleDTOToRoleVOPage(pageInfo);
    }

    @Override
    public PageInfo<RoleVO> queryRolePageWithOutSupplierDistributorRoleId(RoleQueryDTO roleQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        PageInfo<RoleDTO> pageInfo = authUserFacade.queryRoleByPage(roleQueryDTO);
        List<RoleDTO> roleDTOList = pageInfo.getList();
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigService.selectByTenantIdAndConfigKey(loginContextInfoDTO.getTenantId(), TenantConfigEnum.TenantConfig.SUPPLIER_DISTRIBUTOR_ROLE_ID.getConfigKey());
        if (tenantCommonConfig != null) {
            String supplierDistributorRoleId = tenantCommonConfig.getConfigValue();
            List<RoleDTO> result = roleDTOList.stream ().filter (el -> !Objects.equals (el.getId (), Integer.valueOf (supplierDistributorRoleId))).collect (Collectors.toList ());
            if(CollectionUtil.isNotEmpty (result)){
                pageInfo.setList (result);
            }
        }
        return AuthRoleMapper.INSTANCE.roleDTOToRoleVOPage(pageInfo);
    }

    @Override
    public Long querySupplierDistributorRoleId(LoginContextInfoDTO loginContextInfoDTO){
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigService.selectByTenantIdAndConfigKey(loginContextInfoDTO.getTenantId(), TenantConfigEnum.TenantConfig.SUPPLIER_DISTRIBUTOR_ROLE_ID.getConfigKey());
        if (tenantCommonConfig != null) {
            String supplierDistributorRoleId = tenantCommonConfig.getConfigValue();
            return Long.valueOf (supplierDistributorRoleId);
        }
        return null;
    }


}
