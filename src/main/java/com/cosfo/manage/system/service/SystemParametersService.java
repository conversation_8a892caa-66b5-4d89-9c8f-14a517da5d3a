package com.cosfo.manage.system.service;

import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.system.model.po.SystemParameters;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/24 12:47
 */
public interface SystemParametersService {
    /**
     * 查询配置表中的初始icon
     * @return
     */
    ResultDTO<String> listInitIcon();

    /**
     * 根据key查询value
     * @param key
     * @return
     */
    SystemParameters selectByKey(String key);
}
