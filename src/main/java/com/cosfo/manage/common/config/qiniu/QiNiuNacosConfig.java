package com.cosfo.manage.common.config.qiniu;

import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: monna.chen
 * @Date: 2024/1/18 10:35
 * @Description:
 */
@Component
@Data
@Slf4j
public class QiNiuNacosConfig {

    @Value("${qiniu.allow.filetype:image/jpeg;image/png;application/pdf}")
    private String allowFileTyp;

    @Value("${qiniu.access.key:}")
    private String accessKey;

    @Value("${qiniu.secret.key:}")
    private String secretKey;
    @Value("${qiniu.default.bucket:wurth}")
    private String defaultBucket;
    @Value("${qiniu.default.domain:http:}")
    private String defaultDomain;

    /**
     * 创建七牛云上传文件的token
     *
     * @param fileName
     * @param expires
     * @return
     */
    public Map<String, String> createToken(String fileName, long expires) {
        Map<String, String> result = new HashMap();
        Auth auth = Auth.create(accessKey, secretKey);
        // 校验文件是否为真实的图片文件
        StringMap putPolicy = new StringMap();
        putPolicy.put("insertOnly", 1);
        putPolicy.put("mimeLimit", allowFileTyp);
        String token = auth.uploadToken(defaultBucket, fileName, expires, putPolicy);
        result.put("token", token);
        result.put("key", fileName);
        return result;
    }
}
