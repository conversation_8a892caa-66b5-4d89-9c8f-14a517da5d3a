package com.cosfo.manage.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/5/6  11:08
 */
@Getter
@AllArgsConstructor
public enum ResultDTOEnum {

    /**
     * 200-成功
     */
    SUCCESS(200, "成功"),
    /**
     * 403-權限不足
     */
    NO_PERMISSION(403, "登入資訊已失效"),
    /**
     * 404-資源不存在
     */
    NOT_FOUND(404, "資源不存在"),
    /**
     * 500-伺服器錯誤
     */
    SERVER_ERROR(500, "伺服器錯誤"),
    /**
     *
     */
    PARAMETER_MISSING(501, "參數缺失"),
    /**
     * 使用者名稱或密碼錯誤
     */
    USER_OR_PASSWORD_WRONG(502, "使用者名稱或密碼錯誤"),
    /**
     * 帳號失效
     */
    ACCOUNT_FAILURE(503,"帳號失效"),

    /**
     * 1003- 不存在此上傳類型
     */
    UPLOAD_TYPE_NOT_EXIST(1003, "不存在此上傳類型"),

    /**
     * 1005-一級分類圖示不能為空
     */
    PRIMARY_CLASSIFICATION_ICON_EMPTY(1005, "一級分類圖示不能為空"),

    /**
     * 1006-未查詢到使用者資訊
     */
    MERCHANT_INFO_NOT_FOUND(1006, "未查詢到使用者資訊"),

    /**
     * 1008-未查到門市資訊
     */
    STORE_NOT_FOUND(1008, "未查詢到門市資訊"),

    /**
     * 1008-未查到聯絡人資訊
     */
    CONTACT_NOT_FOUND(1009, "未查詢到聯絡人資訊"),

    /**
     * 1010-至少需要有一個聯絡人
     */
    CONTACT_AT_LEAST_ONE(1010, "至少需要有一個聯絡人"),

    /**
     * 1016-門市名稱不符合條件
     */
    STORE_NAME_ERROR(1011, "門市名稱不符合條件，請檢查是否包含特殊符號以及門市名稱長度"),

    /**
     * 運費錯誤
     */
    DELIVERY_FEE_ERROR(1016,"運費不能小於0或者大於9999"),

    /**
     * 運費小數點只可以保留兩位
     */
    DELIVERY_FEE_SCALE_ERROR(1017,"運費小數點只可以保留兩位"),

    /**
     * 該門市狀態非營業中無法關店
     */
    ONLY_SUCCESS_COULD_CLOSE(1023, "該門市狀態非營業中,無法關店"),

    /**
     * 該門市狀態非關店中無法開店
     */
    ONLY_CLOSE_COULD_OPEN(1023, "該門市狀態非關店中,無法開店"),

    /**
     * 基於倉庫報價，對應資料不能為空
     */
    FOLLOW_WAREHOUSE_FAILED(1050, "基於倉庫報價，對應資料不能為空"),

    /**
     * 狀態非待審核
     */
    STATUS_NOT_AUDITING(1051, "狀態非待審核，請確認後重新發起"),

    /**
     * 售後金額不能為空
     */
    TOTAL_PRICE_EMPTY(1052, "售後金額不能為空，請輸入售後金額"),

    /**
     * 售後金額不能小於0
     */
    TOTAL_PRICE_NEGATIVE(1053, "售後金額不能小於0"),

    /**
     * 售後金額不可大於申請金額
     */
    TOTAL_PRICE_TOO_MUCH(1054, "售後金額不可大於申請金額，請確認後重新發起"),

    /**
     * 該門市不存在預設群組
     */
    STORE_NO_EXISTS(1079,"該門市不存在預設群組"),
    /**
     * 缺少分頁參數
     */
    NO_PAGE_PARAMS(1083,"缺少分頁參數"),

    /**
     * 1075-簡訊驗證碼發送失敗
     */
    SEND_CODE_FAIL(1075, "簡訊驗證碼發送失敗"),

    UPDATE_STORE_NO_CHECK_ERROR(1115,"修改時門市編號不可為空"),

    OPERATION_FAIL(1150, "操作失敗"),
    TENANT_NOT_FOUND(1151, "租戶不存在"),
    ;

    private final Integer code;

    private final String message;

    @Override
    public String toString() {
        return "ResultEnum{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }

}
