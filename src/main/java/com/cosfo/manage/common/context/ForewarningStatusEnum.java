package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ForewarningStatusEnum {

    NORMAL(0, "正常"),
    FOREWARNING(1, "预警"),
    SOLD_OUT(2, "售罄"),
    ;

    /**
     * type
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;

    public static String getDescByType(Integer type) {
        for (ForewarningStatusEnum forewarningStatusEnum : ForewarningStatusEnum.values()) {
            if (forewarningStatusEnum.getType().equals(type)) {
                return forewarningStatusEnum.getDesc();
            }
        }
        return null;
    }

    public static void main(String[] args) {
        int compareValue = (int) Math.ceil(1 * 1.00d / 8);
        System.err.println(compareValue);
    }
}
