package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/12 19:55
 */
public class ProductSkuEnum {

    /**
     * 仓库类型
     */
    @Getter
    @AllArgsConstructor
    public enum WarehouseType {

        /**
         * 自营仓
         */
        SELF,
        /**
         * 三方仓
         */
        THIRD
    }

    /**
     * 定价类型
     */
    public enum PriceType {

        /**
         * 指定价
         */
        SPECIFIED_PRICE
    }

    /**
     * 供价方式
     */
    public enum SupplyType {

        /**
         * 0 成本供价
         */
        COST_SUPPLY,

        /**
         * 1 报价单供价
         */
        QUOTATION_SUPPLY
    }

    @Getter
    @AllArgsConstructor
    public enum STORAGE_LOCATION {

        /**
         * 常温
         */
        NORMAL(0, "常温", "18-22"),

        /**
         * 冷藏
         */
        COLD(1, "冷藏","4-10"),

        /**
         * 冷冻
         */
        FROZEN(2, "冷冻","-18--25");

        /**
         * type
         */
        private Integer type;

        /**
         * desc
         */
        private String desc;

        /**
         * 建议储存温度
         */
        private String storageTemperature;

        /**
         * get desc
         *
         * @param type
         * @return
         */
        public static String getDesc(Integer type) {
            for (ProductSkuEnum.STORAGE_LOCATION storageLocationEnum : ProductSkuEnum.STORAGE_LOCATION.values()) {
                if (storageLocationEnum.type.equals(type)) {
                    return storageLocationEnum.desc;
                }
            }
            return null;
        }

        /**
         * get storageTemperature         *
         * @param type
         * @return
         */
        public static String getStorageTemperature(Integer type){
            for (ProductSkuEnum.STORAGE_LOCATION storageLocationEnum : ProductSkuEnum.STORAGE_LOCATION.values()) {
                if (storageLocationEnum.type.equals(type)) {
                    return storageLocationEnum.getStorageTemperature();
                }
            }
            return null;
        }

        /**
         * getType
         * @param storageLocation
         * @return
         */
        public static Integer getType(String storageLocation) {
            for (ProductSkuEnum.STORAGE_LOCATION storageLocationEnum : ProductSkuEnum.STORAGE_LOCATION.values()) {
                if (storageLocationEnum.desc.equals(storageLocation)) {
                    return storageLocationEnum.type;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum GUARANTEE_UNIT {

        /**
         * 天
         */
        DAY(0, "天"),

        /**
         * 月
         */
        MONTH(1, "月"),

        /**
         * 年
         */
        YEAR(2, "年");

        /**
         * type
         */
        private Integer type;

        /**
         * desc
         */
        private String desc;

        /**
         * get desc
         *
         * @param type
         * @return
         */
        public static String getDesc(Integer type) {
            for (ProductSkuEnum.GUARANTEE_UNIT guaranteeUnitEnum : ProductSkuEnum.GUARANTEE_UNIT.values()) {
                if (guaranteeUnitEnum.type.equals(type)) {
                    return guaranteeUnitEnum.desc;
                }
            }
            return null;
        }

        public static Integer getType(String guaranteeUnit) {
            for (ProductSkuEnum.GUARANTEE_UNIT guaranteeUnitEnum : ProductSkuEnum.GUARANTEE_UNIT.values()) {
                if (guaranteeUnitEnum.desc.equals(guaranteeUnit)) {
                    return guaranteeUnitEnum.type;
                }
            }
            return null;
        }
    }
    @Getter
    @AllArgsConstructor
    public enum GUARANTEE_PERIOD_TYPE {

        /**
         * 长期
         */
        LONG_TERM("长期"),

        /**
         * 定期
         */
        PERIODICAL("定期");
        /**
         * desc
         */
        private String desc;

        public static GUARANTEE_PERIOD_TYPE getType(String guaranteeType) {
            for (ProductSkuEnum.GUARANTEE_PERIOD_TYPE guaranteeTypeEnum : ProductSkuEnum.GUARANTEE_PERIOD_TYPE.values()) {
                if (guaranteeTypeEnum.desc.equals(guaranteeType)) {
                    return guaranteeTypeEnum;
                }
            }
            return null;
        }

    }
}
