package com.cosfo.manage.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/26 20:59
 */
@Getter
@AllArgsConstructor
public enum FileDownloadTypeEnum {

    /**
     * 门店信息
     */
    STORE(0, "门店信息下载"),

    /**
     * 商品信息
     */
    PRODUCT(1, "商品信息下载"),

    /**
     * 报价单
     */
    QUOTATION(2, "报价单信息下载"),

    /**
     * 商品销售详情信息下载
     */
    PRODUCT_DETAIL_SALES(3, "商品销售详情信息下载"),

    /**
     * 门店采购信息下载
     */
    MERCHANT_STORE_DETAIL_PURCHASE(4, "门店采购信息下载"),

    /**
     * 代仓商品库存数据
     */
    AGENT_SKU_WAREHOUSE_DATA(5,"代仓商品库存数据"),
    /**
     * 订单信息下载
     */
    ORDER(6, "订单信息下载"),

    /**
     * 售后订单信息下载
     */
    ORDERS_AFTER_SALE(7, "售后订单信息下载"),

    /**
     * 应付信息下载
     */
    PRE_PAY(8, "应付信息下载"),

    /**
     * 门店分组下载
     */
    MERCHANT_STORE_GROUP(9, "门店分组下载"),

    /**
     * 结算单信息下载
     */
    SETTLE_INFO(10,"结算单信息下载"),
    /**
     * 交易流水记录下载
     */
    EXCHANGE_STATEMENT(13,"交易流水记录下载"),
    /**
     * 分账信息下载
     */
    PROFIT_INFO(12,"分账信息下载"),

    /**
     * 采购明细
     */
    PURCHASE_DETAIL_REPORT(14, "采购明细下载"),

    /**
     * 采购退货明细导出
     */
    PURCHASE_BACK_DETAIL_REPORT(15, "采购退货明细下载"),

    /**
     * 货损明细报表
     */
    DAMAGE_DETAIL_REPORT(16, "货损明细下载"),

    /**
     * 损售比明细报表
     */
    DAMAGE_SALE_RATIO_REPORT(17, "损售比明细下载"),

    /**
     * 配送记录
     */
    DISTRIBUTION_RECORD_EXPORT(18,"配送记录"),
    /**
     * 出入库记录
     */
    WAREHOUSE_RECORD_EXPORT(20,"出入库记录"),

    /**
     * 门店采购明细记录
     */
    MERCHANT_STORE_PURCHASE(21,"门店订货明细"),
    /**
     * 采购应付账单
     */
    PURCHASING_BILLS_PAYABLE(22,"采购应付账单"),


    PREPAYMENT_ACCOUNT_EXPORT(23,"余额构成"),

    PREPAYMENT_TRANSACTION_EXPORT(24, "收支明细"),
    PREPAYMENT_RECORD_EXPORT(25,"预付概况"),

    /**
     * 门店余额变动明细记录
     */
    BALANCE_CHANGE_RECORD_EXPORT(26,"门店余额变动明细记录"),

    /**
     * boss后台订单信息下载占用type
     */
    BOSS_ORDER(27,"boss后台订单信息"),

    STORE_DAY_SUMMARY_EXPORT(30, "出入库报表汇总"),

    MERCHANT_STORE_ITEM_DIMENSION(37, "门店商品维度导出"),
    MERCHANT_STORE_DIMENSION(38, "门店维度导出"),
    MERCHANT_STORE_ORDER_PROPORTION(39, "门店订货占比分析"),

    PRODUCT_STOCK_WARN(50,"库存预警数据导出"),

    PRODUCT_SKU_SELF(55,"自营货品列表导出"),


    AGENT_WAREHOUSE_BILL(56,"代仓采购应付"),


    /**
     * 商品信息
     */
    PRODUCT_SKU_SUPPLY(57, "供应商直发货品信息下载"),

    MARKET_ITEM_SALES_RANKING(58, "商品销售榜下载"),

        /**
     * 库存预警
     */
    PRODUCT_STOCK_FOREWARNING_REPORT(59, "库存预警报表"),

    MARKET_ITEM_NO_SALE(62, "滞销商品明细下载"),

    MARKET_ITEM_ON_SALE_SOLD_OUT(63, "售罄商品明细下载"),

    MERCHANT_STORE_PURCHASE_ACTIVITY(64, "门店采购活跃率下载"),

    MERCHANT_STORE_ORDER_FULFILLMENT_RATE(65, "门店订单履约率下载"),
    /**
     * 滞叫分析
     */
    MERCHANT_STORE_ORDER_HYSTERESIS_ANALYSIS(66, "门店滞叫分析表"),

    /**
     * 门店订单稽核报表导出
     */
    POST_ORDER_AUDIT(67,"门店订单稽核报表导出"),

    /**
     * 库存周转天数
     */
    STOCK_TURNOVER_DAYS(69, "库存周转天数"),

    /**
     * 滞销货品
     */
    NO_SALE_GOODS(70, "滞销货品"),

    /**
     * 临期货品
     */
    GOODS_NEAR_DEADLINE_SUMMARY(71, "临期货品"),

    /**
     * 过期货品
     */
    GOODS_EXPIRATION_SUMMARY(72, "临期货品"),

    /**
     * 采购到仓准时率
     */
    SUPPLER_PURCHASE_TO_WAREHOUSE_ON_TIME_RATE(73, "采购到仓准时率"),

    /**
     * 到仓准确率
     */
    SUPPLER_PURCHASE_TO_WAREHOUSE_ACCURACY(74, "到仓准确率"),
    /**
     * 绝配订单导出
     */
    ORDER_JUEPEI(75, "绝配订单导出"),

    /**
     * 采购汇总表商品维度
     */
    PURCHASE_SUMMARY_REPORT_SKU(76, "采购汇总表货品维度"),

    /**
     * 采购汇总表供应商维度
     */
    PURCHASE_SUMMARY_REPORT_SUPPLIER(77, "采购汇总表供应商维度"),

    /**
     * 预售订单设置配送日期结果
     */
    PRESALE_ORDER_SET_DELIVERY_DATE_IMPORT(80, "预售订单设置配送日期结果"),

    // 半成品导出
    SEMI_FINISHED_PRODUCTS(81, "半成品导出"),

    /**
     * 订单对账表
     */
    ORDER_ITEM_STATEMENT_ANALYSIS(82, "订单对账表"),

    /**
     * 商品导入
     */
    ITEM_IMPORT(83, "商品导入"),

    /**
     * 商品价格导入
     */
    ITEM_PRICE_IMPORT(90,"商品价格导入"),

    /**
     * 代下单导入
     */
    AGENT_ORDER_IMPORT(85, "代下单导入"),

    /**
     * 外部导单
     */
    AGENT_ORDER_IMPORT_OUTER(86, "外部导单"),


    IMPORT_BASE_PRICE(184, "导入基础价格"),
    /**
     * 客戶導入
     */
    IMPORT_CUS_BASE_PRICE(190, "客户导入"),
    IMPORT_CUS_PRICE(185, "导入客户价格"),
    ;

    /**
     * type
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;
}
