//package com.cosfo.manage.common.service;
//
//import com.cosfo.manage.common.model.dto.DocCodeMappingDTO;
//
//import java.util.List;
//import java.util.Set;
//
///**
// * <AUTHOR>
// */
//public interface DocCodeMappingService {
//
//    /**
//     * 保存映射
//     * @param mappingDTOS
//     * @return
//     */
//    boolean saveCodeMapping(List<DocCodeMappingDTO> mappingDTOS);
//
//
//    /**
//     * 根据 targetCode 和 type 查询映射
//     */
//    List<DocCodeMappingDTO> selectByTargetCodeAndType(List<String> targetCodeList, Integer targetType, Long tenantId, Integer channelType);
//
//
//    /**
//     * 根据 outCode 和 targetType 以及 channelType 来查询
//     */
//    List<DocCodeMappingDTO> selectByOutCodeAndType(Set<String> outCodeList, Integer targetType, Integer channelType, Long tenantId);
//
//    /**
//     * 根据ids批量删除映射
//     */
//    boolean deleteByIds(List<Long> ids);
//
//}
