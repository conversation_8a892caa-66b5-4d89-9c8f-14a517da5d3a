package com.cosfo.manage.common.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.context.FileDownloadStatusEnum;
import com.cosfo.manage.common.exception.DefaultServiceException;
import com.cosfo.manage.common.mapper.CommonLocationCityMapper;
import com.cosfo.manage.common.mapper.CommonLocationProvinceMapper;
import com.cosfo.manage.common.model.po.CommonLocationCity;
import com.cosfo.manage.common.model.vo.CommonLocationProvinceVO;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.FileUtil;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import com.cosfo.manage.file.model.po.FileDownloadRecord;
import com.cosfo.manage.file.service.FileDownloadRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 描述: 通用服务类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/19
 */
@Slf4j
@Service
public class CommonServiceImpl implements CommonService {

    @Resource
    private CommonLocationCityMapper commonLocationCityMapper;
    @Resource
    private CommonLocationProvinceMapper commonLocationProvinceMapper;

    @Resource
    private FileDownloadRecordService fileDownloadRecordService;

    @Override
    public void downloadFile(HttpServletResponse response, String fileName, String filePath) {
        String currentDay = TimeUtils.getTodayString(TimeUtils.FORMAT_STRING);
        setExcelDownloadResponseHeaders(response, fileName + currentDay);
        copyFileToResponse(filePath, response);
        deleteFile(filePath);
    }

    /**
     * 针对excel下载设置对应的返回头
     *
     * @param response {@link HttpServletResponse}
     * @param fileName 文件名称
     */
    public void setExcelDownloadResponseHeaders(HttpServletResponse response, String fileName) {
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        try {
            response.setHeader("Access-Control-Expose-Headers", "filename");
            response.setHeader("Content-disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName + ExcelTypeEnum.XLSX.getValue(), StandardCharsets.UTF_8.name()));
            response.setHeader("filename", URLEncoder.encode(fileName + ExcelTypeEnum.XLSX.getValue(), StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            throw new DefaultServiceException("檔案下載失敗！");
        }
    }

    /**
     * 将文件内容放到 response中
     *
     * @param filePath 文件路径
     * @param response {@link HttpServletResponse}
     */
    public void copyFileToResponse(String filePath, HttpServletResponse response) {
        try {
            Files.copy(Paths.get(filePath), response.getOutputStream());
        } catch (IOException e) {
            throw new DefaultServiceException("檔案下載失敗！");
        }
    }

    /**
     * 文件删除
     *
     * @param filePath 文件路径
     */
    @Override
    public void deleteFile(String filePath) {
        try {
            Files.delete(Paths.get(filePath));
        } catch (IOException e) {
            throw new DefaultServiceException("檔案下載失敗！");
        }
    }

    @Override
    public String exportExcel(List list, String name) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), name);
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        excelWriter.fill(list, fillConfig, writeSheet);
        excelWriter.finish();
        return filePath;
    }

    @Override
    public Boolean uploadExcelAndUpdateDownloadStatus(String filePath, String fileName, com.cosfo.manage.common.context.ExcelTypeEnum excelTypeEnum, Long fileDownloadRecordId){
        if (StringUtils.isBlank(fileName)) {
            fileName = excelTypeEnum.getDesc() + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH_mm_ss");
        }
        String qiNiuFilePath = null;
        try {
            qiNiuFilePath = QiNiuUtils.uploadFile(filePath, fileName + ".xlsx");
        } catch (IOException e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }

        //删除临时文件
        deleteFile(filePath);

        // 更新文件下载记录
        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
        fileDownloadRecord.setId(fileDownloadRecordId);

        Boolean successUploadFlag = Objects.nonNull(qiNiuFilePath);
        if (!successUploadFlag) {
            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FAIL.getStatus());
        } else {
            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FINISHED.getStatus());
            fileDownloadRecord.setUrl(qiNiuFilePath);
        }
        fileDownloadRecordService.updateSelectiveByPrimaryKey(fileDownloadRecord);
        return successUploadFlag;
    }


    @Override
    public Boolean generateAndUploadExcel(List dataList, com.cosfo.manage.common.context.ExcelTypeEnum excelTypeEnum, Long fileDownloadRecordId) {
        String filePath = exportExcel(dataList, excelTypeEnum.getName());
        String qiNiuFilePath = null;
        try {
            qiNiuFilePath = QiNiuUtils.uploadFile(filePath, excelTypeEnum.getDesc() + TimeUtils.changeDate2String(new Date(), "yyyy-MM-dd HH_mm_ss") + ".xlsx");
        } catch (IOException e) {
            log.error("异常信息：{}", e.getMessage(), e);
        }

        //删除临时文件
        deleteFile(filePath);

        // 更新文件下载记录
        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
        fileDownloadRecord.setId(fileDownloadRecordId);

        Boolean successUploadFlag = Objects.nonNull(qiNiuFilePath);
        if (!successUploadFlag) {
            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FAIL.getStatus());
        } else {
            fileDownloadRecord.setStatus(FileDownloadStatusEnum.FINISHED.getStatus());
            fileDownloadRecord.setUrl(qiNiuFilePath);
        }
        fileDownloadRecordService.updateSelectiveByPrimaryKey(fileDownloadRecord);
        return successUploadFlag;
    }

    @Override
    public String exportExcel(List list, String name, CellWriteHandler cellWriteHandler) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), name);
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerWriteHandler(cellWriteHandler).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(list, fillConfig, writeSheet);
        excelWriter.finish();
        return filePath;
    }

    @Override
    public List<CommonLocationCity> queryCitiesByProvinceId(Long provinceId) {
        return commonLocationCityMapper.queryCitiesByProvinceId(provinceId);
    }

    @Override
    public List<CommonLocationProvinceVO> cityList() {
        List<CommonLocationProvinceVO> locationProvinceVOS = commonLocationProvinceMapper.listAll();
        return  locationProvinceVOS;
    }

    /**
     * 上传文件
     *
     * @param file
     * @param filePath
     */
    @Override
    public void uploadExcelFile(File file, String filePath) {
        try {
            MultipartFile multipartFile = FileUtil.fileToMultipartFile(file);
            String s = QiNiuUtils.uploadFile(multipartFile, Constants.FILE_DIR + file.getName());
            FileUtil.deleteFile(filePath);
        } catch (IOException e) {
            log.error("上传excel文件失败");
            throw new DefaultServiceException("導出報表失敗");
        }
    }
}
