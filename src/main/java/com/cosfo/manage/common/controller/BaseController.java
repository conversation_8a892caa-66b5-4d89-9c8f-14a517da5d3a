package com.cosfo.manage.common.controller;

import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.UserLoginContextUtils;
import net.xianmu.common.exception.BizException;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public abstract class BaseController {

    public LoginContextInfoDTO getMerchantInfoDTO() {
        LoginContextInfoDTO merchantInfoDTO = UserLoginContextUtils.getMerchantInfoDTO();
        if (merchantInfoDTO == null) {
            throw new BizException("登錄帳號不存在");
        }
        return merchantInfoDTO;
    }

    public String getToken() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        HttpServletRequest request = requestAttributes.getRequest();
        return request.getHeader("token");
    }
}
