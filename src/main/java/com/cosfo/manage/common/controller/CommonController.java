package com.cosfo.manage.common.controller;

import com.cosfo.manage.common.model.vo.CommonLocationProvinceVO;
import com.cosfo.manage.common.service.CommonService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 公共
 *
 * <AUTHOR>
 * @date 2022/9/28 11:58
 */
@RestController
@RequestMapping(value = "/common")
public class CommonController {

    @Resource
    private CommonService commonService;

    /**
     * 城市下拉列表
     *
     * @return
     */
    @PostMapping("/area/query/city-list")
    public CommonResult<List<CommonLocationProvinceVO>> cityList() {
        List<CommonLocationProvinceVO> provinceVOS = commonService.cityList();
        return CommonResult.ok(provinceVOS);
    }
}
