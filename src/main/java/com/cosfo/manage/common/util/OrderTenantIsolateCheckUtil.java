package com.cosfo.manage.common.util;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.order.model.vo.OrderVO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class OrderTenantIsolateCheckUtil {

    public static void check(OrderVO orderVO) {
        LoginContextInfoDTO merchantInfoDTO = UserLoginContextUtils.getMerchantInfoDTO();
        if (merchantInfoDTO.getTenantId() == null) {
            log.warn("当前查询到订单信息={}, 且用户不存在租户下笔={}",JSON.toJSONString(orderVO), JSON.toJSONString(merchantInfoDTO));
            return;
        }
        if (!Objects.equals(orderVO.getTenantId(), merchantInfoDTO.getTenantId())) {
            log.warn("当前订单和租户信息不匹配, orderId={}, 当前用户信息={}", orderVO.getOrderId(), JSON.toJSONString(merchantInfoDTO));
            throw new BizException("訂單不存在");
        }
    }
}
