package com.cosfo.manage.common.util;

import net.xianmu.common.exception.BizException;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/13
 */
public class AESUtils {

    private final static String ASE_KEY = "bb3ba89aaf302asf";
    private final static String PREFIX_FLAG = "h5mall_";

    /**
     * ASE对称加密
     *
     * @param content
     * @return
     * @throws Exception
     */
    public static String encrypt(String content) {
        try {
            SecretKeySpec key = new SecretKeySpec(ASE_KEY.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] encryptedBytes = cipher.doFinal(content.getBytes("UTF-8"));
            return PREFIX_FLAG + Base64.getEncoder().encodeToString(encryptedBytes);
        }catch (Exception e){
            throw new BizException("生成未登錄token失敗");
        }
    }
}
