package com.cosfo.manage.common.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.cosfo.manage.common.exception.DefaultServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StreamUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.cosfo.manage.common.constant.Constants;

public class ExcelUtils {
    private static final Logger logger = LoggerFactory.getLogger(ExcelUtils.class);

    private static final int BUFFER_SIZE = 8192;

    private ExcelUtils() {
    }

    /**
     * 将文件内容放到 response中
     *
     * @param filePath 文件路径
     * @param response {@link HttpServletResponse}
     */
    public static void copyFileToResponse(String filePath, HttpServletResponse response) {
        try {
            Files.copy(Paths.get(filePath), response.getOutputStream());
        } catch (IOException e) {
            logger.error("文件copy失败", e);
        }
    }

    /**
     * 将文件的byte[]复制到response中
     *
     * @param bytes
     * @param response
     */
    public static void copyBytesToResponse(byte[] bytes, HttpServletResponse response) {
        try {
            copyBytesToOutputStream(bytes, response.getOutputStream());
        } catch (IOException e) {
            logger.error("文件copy失败", e);
        }
    }

    /**
     * 针对excel下载设置对应的返回头
     *
     * @param response {@link HttpServletResponse}
     * @param fileName 文件名称
     */
    public static void setExcelDownloadResponseHeaders(HttpServletResponse response, String fileName) {
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        try {
            response.setHeader("Content-disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName + ExcelTypeEnum.XLSX.getValue(), StandardCharsets.UTF_8.name()));
            response.setHeader("filename", URLEncoder.encode(fileName + ExcelTypeEnum.XLSX.getValue(), StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            logger.error("encode fileName failure");
        }
    }

    /**
     * 文件删除
     *
     * @param filePath 文件路径
     */
    public static void deleteFile(String filePath) {
        try {
            Files.delete(Paths.get(filePath));
        } catch (IOException e) {
            logger.error("删除文件失败", e);
        }
    }

    /**
     * 获得文件缓存名称
     *
     * @return
     */
    public static String tempExcelFilePath() {
        return System.getProperty("user.dir") + File.separator + tempExcelFileName();
    }

    /**
     * xlsx类型excel文件临时名称
     *
     * @return 文件名称
     */
    public static String tempExcelFileName() {
        return System.currentTimeMillis() + ExcelTypeEnum.XLSX.getValue();
    }

    /**
     * 字节 ==> 输出流
     *
     * @param bytes        字节数组
     * @param outputStream 输出流
     */
    public static void copyBytesToOutputStream(byte[] bytes, OutputStream outputStream) {
        Objects.requireNonNull(bytes);

        byte[] tempByte = new byte[BUFFER_SIZE];
        int n;
        try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
            while ((n = inputStream.read(tempByte)) > 0) {
                outputStream.write(tempByte, 0, n);
            }
        } catch (IOException e) {
            logger.error("", e);
        }
    }

    /**
     * 从resource目录下读取文件
     *
     * @param directory 文件夹名
     * @param fileName  文件名
     * @return
     */
    public static String getResourceFileName(Class clazz, String directory, String fileName) {

        return clazz.getClass().getResource("/").getPath() + directory + Constants.SLASH + fileName;
    }

    /**
     * 获得jar包中resource目录下的文件
     *
     * @param fileName
     * @return
     */
    public static byte[] getExcelFile(Class clazz, String fileName) {
        try (InputStream inputStream = clazz.getClassLoader()
                .getResourceAsStream(getExcelFilePath(fileName))) {

            return StreamUtils.copyToByteArray(inputStream);
        } catch (IOException e) {
            logger.error("获取文件失败", e);
            return null;
        }
    }

    /**
     * 获得文件的InputStream
     *
     * @param fileName
     * @return
     */
    public static InputStream getExcelFileInputStream(Class clazz, String fileName) {

        InputStream inputStream = clazz.getClassLoader()
                .getResourceAsStream(getExcelFilePath(fileName));

        return inputStream;
    }

    /**
     * 获得文件相对路径
     *
     * @param fileName
     * @return
     */
    public static String getExcelFilePath(String fileName) {
        return Constants.EXCEL_DIRECTORY + Constants.SLASH + fileName;
    }

    /**
     * 设置excel样式
     *
     * @return
     */
    public static HorizontalCellStyleStrategy getExcelStyleStrategy() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 20);
        headWriteFont.setFontName("思源黑体");
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setLocked(false);

        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName("宋体");
        contentWriteFont.setFontHeightInPoints((short) 15);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setLocked(false);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    public static <T> List<T> read(String filePath, final Class<?> clazz) {
        File f = new File(filePath);
        try (FileInputStream fis = new FileInputStream(f)) {
            return read(fis, clazz);
        } catch (FileNotFoundException e) {
            logger.error("文件{}不存在", filePath, e);
        } catch (IOException e) {
            logger.error("文件读取出错", e);
        }
        return null;
    }

    public static <T> List<T> read(InputStream inputStream, final Class<?> clazz) {
        if (inputStream == null) {
            throw new DefaultServiceException("系統異常");
        }
        // 有个很重要的点 DataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
        DataListener<T> listener = new DataListener<>();
        // 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        EasyExcel.read(inputStream, clazz, listener).sheet().doRead();
        return listener.getRows();
    }

    public static void write(String outFile, List<?> list) {
        Class<?> clazz = list.get(0).getClass();
        EasyExcel.write(outFile, clazz).sheet().doWrite(list);
    }

    public static void write(String outFile, List<?> list, String sheetName) {
        Class<?> clazz = list.get(0).getClass();
        EasyExcel.write(outFile, clazz).sheet(sheetName).doWrite(list);
    }

    public static void write(OutputStream outputStream, List<?> list, String sheetName) {
        Class<?> clazz = list.get(0).getClass();
        // sheetName为sheet的名字，默认写第一个sheet
        EasyExcel.write(outputStream, clazz).sheet(sheetName).doWrite(list);
    }

    /**
     * 文件下载（失败了会返回一个有部分数据的Excel），用于直接把excel返回到浏览器下载
     */
    public static void download(HttpServletResponse response, List<?> list, String sheetName) throws IOException {
        Class<?> clazz = list.get(0).getClass();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode(sheetName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), clazz).head(clazz).sheet(sheetName).doWrite(list);
    }
}

class DataListener<T> extends AnalysisEventListener<T> {

    private static final Logger logger = LoggerFactory.getLogger(DataListener.class);

    private final List<T> rows = new ArrayList<>();

    @Override
    public void invoke(T t, AnalysisContext analysisContext) {
        rows.add(t);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        logger.info("解析完成！读取{}行", rows.size());
    }

    public List<T> getRows() {
        return rows;
    }
}
