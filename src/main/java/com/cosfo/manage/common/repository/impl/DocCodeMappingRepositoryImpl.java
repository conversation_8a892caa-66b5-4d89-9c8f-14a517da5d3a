//package com.cosfo.manage.common.repository.impl;
//
//import cn.hutool.core.collection.CollectionUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.cosfo.manage.common.model.po.DocCodeMapping;
//import com.cosfo.manage.common.mapper.DocCodeMappingMapper;
//import com.cosfo.manage.common.repository.DocCodeMappingRepository;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.Set;
//
///**
// * <p>
// * 映射关系 服务实现类
// * </p>
// *
// * <AUTHOR>
// * @since 2023-11-10
// */
//@Service
//public class DocCodeMappingRepositoryImpl extends ServiceImpl<DocCodeMappingMapper, DocCodeMapping> implements DocCodeMappingRepository {
//
//    @Override
//    public List<DocCodeMapping> selectByTargetCodeAndType(List<String> targetCodeList, Integer targetType, Integer channelType, Long tenantId) {
//        LambdaQueryWrapper<DocCodeMapping> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(DocCodeMapping::getTenantId, tenantId);
//        queryWrapper.in(CollectionUtil.isNotEmpty (targetCodeList),DocCodeMapping::getTargetCode, targetCodeList);
//        queryWrapper.eq(DocCodeMapping::getTargetType, targetType);
//        queryWrapper.eq(channelType != null, DocCodeMapping::getChannelType, channelType);
//        return list(queryWrapper);
//    }
//
//    @Override
//    public List<DocCodeMapping> selectByOutCodeAndType(Set<String> outCodeList, Integer targetType, Integer channelType, Long tenantId) {
//        LambdaQueryWrapper<DocCodeMapping> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(DocCodeMapping::getTenantId, tenantId);
//        queryWrapper.in(DocCodeMapping::getOutCode, outCodeList);
//        queryWrapper.eq(DocCodeMapping::getTargetType, targetType);
//        queryWrapper.eq(DocCodeMapping::getChannelType, channelType);
//        return list(queryWrapper);
//    }
//
//    @Override
//    public void deleteByIds(List<Long> ids) {
//        baseMapper.deleteBatchIds(ids);
//    }
//
//    @Override
//    public void batchInsert(List<DocCodeMapping> docCodeMappingList) {
//        baseMapper.batchInsert(docCodeMappingList);
//    }
//}
