package com.cosfo.manage.common.sms.impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.context.SMSTypeEnum;
import com.cosfo.manage.common.context.SenderPlatformEnum;
import com.cosfo.manage.common.sms.SmsSender;
import com.cosfo.manage.common.sms.mapper.SmsSceneMapper;
import com.cosfo.manage.common.sms.model.Sms;
import com.cosfo.manage.common.sms.model.SmsScene;
import com.cosfo.manage.system.mapper.SystemParametersMapper;
import com.cosfo.manage.system.model.po.SystemParameters;
import org.slf4j.Logger;
import org.springframework.http.HttpEntity;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.slf4j.LoggerFactory.getLogger;

/**
 * <AUTHOR>
 * @discription 创蓝短信
 * @date 2022/5/21 15:57
 */
@Component
public class ChuangLanSmsSender implements SmsSender {

    private static Logger logger = getLogger(ChuangLanSmsSender.class);

    public static final String ACCOUNT_KEY = "ChuangLanAccount";
    public static final String PASSWORD_KEY = "ChuangLanPassword";
    public static final String MARKET_ACCOUNT_KEY = "ChuangLanMarketAccount";
    public static final String MARKET_PASSWORD_KEY = "ChuangLanMarketPassword";
    public static final String FORMATTER = "yyyyMMddHHmm";
    public static final String FAN_TAI_SIGNATURE = "【帆台】";

    @Resource
    private SystemParametersMapper systemParametersMapper;

    @Resource
    private SmsSceneMapper smsSceneMapper;

    @Override
    public boolean sendSms(Sms sms) {
        String content = StringUtils.isEmpty(sms.getContent()) ? renderTemp(sms.getSceneId(), sms.getArgs()) : sms.getContent();
        String account = getAccount(sms.getType());
        String password = getPassword(sms.getType());
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("account", account);
        jsonObject.put("password", password);
        jsonObject.put("msg", content);
        jsonObject.put("phone", sms.getPhone());
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern(FORMATTER));
        jsonObject.put("sendtime", now);

        HttpEntity<String> request = new HttpEntity<>(jsonObject.toString());

        Map<String, Object> result = restTemplate.postForObject(Constants.CHUANGLAN_SEND_URL, request, HashMap.class);
        logger.info("创蓝短信发送结果：{}", result);

        boolean success = result != null && "0".equals(result.get("code"));
        if (!success) {
            logger.warn("创蓝短信发送失败: result:{}, jsonObject:{}", result, jsonObject);
        }
        return success;
    }

    /**
     * 根据类型加载帐号，默认加载通知帐号
     *
     * @param type 短信类型
     * @return 密码
     */
    private String getPassword(SMSTypeEnum type) {
        if (SMSTypeEnum.NOTIFY == type || type == null) {
            SystemParameters parameters = systemParametersMapper.selectByKey(PASSWORD_KEY);
            return parameters.getParamValue();
        }
        if (SMSTypeEnum.MARKET == type) {
            SystemParameters parameters = systemParametersMapper.selectByKey(MARKET_PASSWORD_KEY);
            return parameters.getParamValue();
        }
        logger.error("unknown SMSType");
        return "";
    }

    /**
     * 根据类型加载帐号，默认加载通知帐号
     *
     * @param type 短信类型
     * @return 帐号
     */
    private String getAccount(SMSTypeEnum type) {
        if (SMSTypeEnum.NOTIFY == type || type == null) {
            SystemParameters parameters = systemParametersMapper.selectByKey(ACCOUNT_KEY);
            return parameters.getParamValue();
        }
        if (SMSTypeEnum.MARKET == type) {
            SystemParameters parameters = systemParametersMapper.selectByKey(MARKET_ACCOUNT_KEY);
            return parameters.getParamValue();
        }
        logger.error("unknown SMSType");
        return "";
    }

    /**
     * 渲染短信结果
     *
     * @param sceneId 场景id
     * @param args    参数
     * @return 短信内容
     */
    public String renderTemp(Long sceneId, List<String> args) {
        SmsScene smsScene = smsSceneMapper.selectByScenePlatform(sceneId, SenderPlatformEnum.CHUANGLAN_SMS.ordinal());
        String t = smsScene.getTemplate();
        for (String arg : args) {
            t = t.replaceFirst("\\{s\\}", arg);
        }
        return FAN_TAI_SIGNATURE + t;
    }
}
