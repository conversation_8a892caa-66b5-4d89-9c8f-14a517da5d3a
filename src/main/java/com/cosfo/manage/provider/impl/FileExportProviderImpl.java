package com.cosfo.manage.provider.impl;

import com.cosfo.manage.client.file.FileExportProvider;
import com.cosfo.manage.client.file.req.TenantBillQueryReq;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @author: xiaowk
 * @time: 2024/1/10 下午3:31
 */
@DubboService
@Slf4j
public class FileExportProviderImpl implements FileExportProvider {

    @Override
    public DubboResponse<Boolean> tenantBillExport(TenantBillQueryReq tenantBillQueryReq) {

//

        return DubboResponse.getOK(true);
    }
}
