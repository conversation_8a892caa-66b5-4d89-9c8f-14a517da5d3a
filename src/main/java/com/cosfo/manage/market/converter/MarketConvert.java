package com.cosfo.manage.market.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.cofso.item.client.enums.MarketItemUnitTypeEnum;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.item.client.resp.MarketItemUnitResp;
import com.cofso.item.client.resp.MarketItemWithClassificationResp;
import com.cofso.page.PageResp;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.model.vo.*;
import com.github.pagehelper.PageInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

/**
 * @author: monna.chen
 * @Date: 2023/4/27 18:18
 * @Date: 2023/4/17 13:33
 * @Description:
 */
@Mapper
public interface MarketConvert {

    MarketConvert INSTANCE = Mappers.getMapper(MarketConvert.class);

    /**
     * 新增market 接口返回参数转换
     *
     * @param resDTO
     * @return
     */
    MarketAddResVO convert2ResVO(MarketAddResDTO resDTO);


    @Mapping(source = "itemTitle", target = "title")
    @Mapping(source = "itemId", target = "id")
    MarketItemQueryDTO convert2QueryDto(CombineItemReqVO reqVO);

    @Mapping(source = "pageIndex", target = "pageNum")
    CombineMarketQueryDTO convert2queryDto(CombineMarketQueryReqVO queryReqVO);

    PageInfo<CombineMarketListResVO> convert2listResVo(PageInfo<CombineMarketListDTO> pageInfo);

    @Mapping(source = "combineItemList", target = "makertCombineItemMappingVOList")
    CombineMarketDetailVO convert2CombineDetailVo(CombineMarketDetailDTO detailDTO);

    @Mapping(source = "marketItemId", target = "itemId")
    MakertCombineItemMappingVO convert2MappingVO(CombineItemDTO dto);

    @Mapping(source = "marketCombineItemMappingVOList", target = "combineItemList")
    CombineInputDTO convert2InputDto(MarketCombineItemInputVO inputVO);

    @Mapping(source = "itemId", target = "marketItemId")
    CombineItemDTO convert2CombineItemDto(MakertCombineItemMappingVO vo);

    @Mapping(source = "itemId", target = "id")
    @Mapping(source = "stockAmount", target = "amount")
    MarketItemDTO convert2ItemDto(MarketItemInfoDTO infoDTO);

    @Mapping(source = "id", target = "itemId")
    @Mapping(source = "marketItemUnfairPriceStrategyDTO", target = "marketItemUnfairPriceStrategyVO")
    MarketItemPriceStrategyVO convert2VO(MarketItemDTO dto);

    @Mapping(target = "size", source = "pages")
    PageInfo<MarketItemPageVo> respToVoPage(PageResp<MarketItemWithClassificationResp> resp);

    @Mapping(target = "size", source = "pages")
    PageInfo<MarketItemInfoPageVO> respToItemPageVO(PageResp<MarketItemInfoResp> resp);

    @Mapping(source = "itemTitle", target = "spuTitle")
    @Mapping(source = "itemTitle", target = "itemTitle")
    @Mapping(source = "itemTitle", target = "title")
    @Mapping(source = "itemId", target = "id")
    @Mapping(source = "marketItemClassificationResp.secondClassificationId", target = "secondClassificationId")
    @Mapping(source = "marketItemClassificationResp.secondClassificationName", target = "secondClassificationName")
    @Mapping(source = "marketItemClassificationResp.secondClassificationId", target = "classificationId")
    @Mapping(source = "marketItemClassificationResp.firstClassificationId", target = "firstClassificationId")
    @Mapping(source = "marketItemClassificationResp.firstClassificationName", target = "firstClassificationName")
    @Mapping(source = "marketItemClassificationResp.classificationFullName", target = "marketItemClassificationStr")
    @Mapping(source = "categoryId", target = "thirdCategoryId")
    @Mapping(source = "priceStr", target = "priceRange")
    @Mapping(source = "marketItemUnitList", target = "storeOrderingUnit" ,qualifiedByName = "getStoreOrderingUnit")
    @Mapping(source = "marketItemUnitList", target = "storeInventoryUnit" ,qualifiedByName = "getStoreInventoryUnit")
    @Mapping(source = "marketItemUnitList", target = "storeCostUnit" ,qualifiedByName = "getStoreCostUnit")
    @Mapping(source = "marketItemUnitList", target = "storeInventoryCostUnitMultiple" ,qualifiedByName = "getStoreInventoryCostUnitMultiple")
    @Mapping(source = "marketItemUnitList", target = "storeOrderingInventoryUnitMultiple" ,qualifiedByName = "getStoreOrderingInventoryUnitMultiple")
    MarketItemInfoPageVO respToItemPageVOItem(MarketItemInfoResp respItem);

    @Named("getStoreOrderingUnit")
    static String getStoreOrderingUnit(List<MarketItemUnitResp> marketItemUnitList){
        return Optional.ofNullable (getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_ORDERING_UNIT)).map (MarketItemUnitResp::getUnitDesc).orElse (null);
    }
    @Named("getStoreInventoryUnit")
    static String getStoreInventoryUnit(List<MarketItemUnitResp> marketItemUnitList){
        return Optional.ofNullable (getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_INVENTORY_UNIT)).map (MarketItemUnitResp::getUnitDesc).orElse (null);
    }
    @Named("getStoreCostUnit")
    static String getStoreCostUnit(List<MarketItemUnitResp> marketItemUnitList){
        return Optional.ofNullable (getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_COST_UNIT)).map (MarketItemUnitResp::getUnitDesc).orElse (null);
    }

    @Named("getUnitByEnum")
    static MarketItemUnitResp getUnitByEnum(List<MarketItemUnitResp> marketItemUnitList,MarketItemUnitTypeEnum storeOrderingUnit) {
        if(CollectionUtil.isEmpty (marketItemUnitList)){
            return null;
        }
        Optional<MarketItemUnitResp> first = marketItemUnitList.stream ().filter (e -> storeOrderingUnit.getCode ().equals (e.getUnitType ())).findFirst ();
        if(first.isPresent ()){
            return first.get ();
        }
        return null;
    }

    @Named("getStoreInventoryCostUnitMultiple")
    static BigDecimal getStoreInventoryCostUnitMultiple(List<MarketItemUnitResp> marketItemUnitList){
        MarketItemUnitResp inventoryUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_INVENTORY_UNIT);
        MarketItemUnitResp costUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_COST_UNIT);
        if(inventoryUnit !=null && costUnit != null){
            return costUnit.getStoreOrderingUnitMultiple ().divide (inventoryUnit.getStoreOrderingUnitMultiple (), RoundingMode.HALF_UP);
        }
        return null;
    }
    @Named("getStoreOrderingInventoryUnitMultiple")
    static BigDecimal getStoreOrderingInventoryUnitMultiple(List<MarketItemUnitResp> marketItemUnitList){
        MarketItemUnitResp inventoryUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_INVENTORY_UNIT);
        MarketItemUnitResp orderingUnit = getUnitByEnum (marketItemUnitList, MarketItemUnitTypeEnum.STORE_ORDERING_UNIT);
        if(inventoryUnit!=null && orderingUnit!=null){
            return inventoryUnit.getStoreOrderingUnitMultiple ().divide (orderingUnit.getStoreOrderingUnitMultiple (), RoundingMode.HALF_UP);
        }
        return null;
    }
}
