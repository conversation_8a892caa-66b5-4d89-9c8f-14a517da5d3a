package com.cosfo.manage.market.controller;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.util.transfer.MarketClassificationTreeTransferUtil;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.market.model.dto.MarketClassificationDTO;
import com.cosfo.manage.market.model.dto.MarketClassificationQueryDTO;
import com.cosfo.manage.market.model.vo.MarketClassificationTreeVO;
import com.cosfo.manage.market.model.vo.MarketClassificationVO;
import com.cosfo.manage.market.service.MarketClassificationService;
import com.cosfo.manage.system.service.SystemParametersService;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 分类 Controller层
 * @date 2022/5/10 9:49
 */
@RestController
@RequestMapping("/marketClassification")
public class MarketClassificationController extends BaseController {

    @Resource
    private MarketClassificationService marketClassificationService;
    @Resource
    private SystemParametersService systemParametersService;

    /**
     * 保存分类信息
     * @param marketClassificationVO
     * @param bindingResult
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:goods-front-category:add", expireError = true)
    @RequestMapping(value = "/saveClassification", method = RequestMethod.POST)
    public ResultDTO saveClassification(@Validated @RequestBody MarketClassificationVO marketClassificationVO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return  ResultDTO.fail(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        }
        MarketClassificationDTO classificationDTO = new MarketClassificationDTO();
        BeanUtils.copyProperties(marketClassificationVO, classificationDTO);
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return marketClassificationService.saveClassification(classificationDTO, contextInfoDTO);
    }

    /**
     * 分类树
     * @return
     */
    @RequestMapping(value = "/listAll", method = RequestMethod.GET)
    public ResultDTO selectClassificationTree() {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return marketClassificationService.selectClassificationTree(contextInfoDTO.getTenantId());
    }

    /**
     * 根据id查询分类
     * @param id
     * @return
     */
    @RequestMapping(value = "/listAllById", method = RequestMethod.GET)
    public ResultDTO listAllById(Long id) {
        return marketClassificationService.listAllById(id);
    }

    /**
     * 删除分类
     * @param marketClassificationVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:goods-front-category:delete", expireError = true)
    @RequestMapping(value = "/deleteClassification", method = RequestMethod.DELETE)
    public ResultDTO deleteClassification(@RequestBody MarketClassificationVO marketClassificationVO) {
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        Long id = marketClassificationVO.getId();
        return marketClassificationService.deleteClassification(id, contextInfoDTO);
    }

    /**
     * 更新分类
     * @param marketClassificationVO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @TenantPrivilegesPermission(permissionCode = "cosfo_manage:goods-front-category:update", expireError = true)
    @RequestMapping(value = "/updateClassification", method = RequestMethod.PUT)
    public ResultDTO updateClassification(@RequestBody MarketClassificationVO marketClassificationVO) {
        MarketClassificationDTO marketClassificationDTO = new MarketClassificationDTO();
        BeanUtils.copyProperties(marketClassificationVO, marketClassificationDTO);
        LoginContextInfoDTO contextInfoDTO = getMerchantInfoDTO();
        return marketClassificationService.updateClassification(marketClassificationDTO, contextInfoDTO);
    }

    /**
     * 分类排序
     * @param marketClassificationTreeVOList
     * @return
     */
    @RequestMapping(value = "/sort", method = RequestMethod.POST)
    public ResultDTO sort(@RequestBody List<MarketClassificationTreeVO> marketClassificationTreeVOList) {
        return marketClassificationService.sort(MarketClassificationTreeTransferUtil.toMarketClassificationTreeDTO(marketClassificationTreeVOList), getMerchantInfoDTO());
    }

    @RequestMapping(value = "/listInitIcon", method = RequestMethod.GET)
    public ResultDTO<String> listInitIcon() {
        return systemParametersService.listInitIcon();
    }

    /**
     * 根据classificationIds查询商品分组树
     *
     * @return
     */
    @PostMapping(value = "query/tree-classificationIds")
    public CommonResult<List<MarketClassificationVO>> queryByClassificationIds(@RequestBody MarketClassificationQueryDTO marketClassificationQueryDTO){
        List<MarketClassificationVO> marketClassificationVOS = marketClassificationService.queryByClassificationIds(marketClassificationQueryDTO, getMerchantInfoDTO());
        return CommonResult.ok(marketClassificationVOS);
    }
}
