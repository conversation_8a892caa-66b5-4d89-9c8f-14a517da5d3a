package com.cosfo.manage.market.model.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/11
 */
@Data
public class MarketItemInput {
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 销售主键Id
     */
    private Long marketId;
    /**
     * 主键Id
     */
    private Long id;
    /**
     * 0 自营1三方
     */
    @Deprecated
    private Integer warehouseType;
    /**
     * 配送方式 0自营 1三方
     */
    @Deprecated
    private Integer deliveryType;
    /**
     * 货源skuId
     */
    private Long skuId;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格备注
     */
    private String weightNotes;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 供应商Id
     */
    private String supplierId;
    /**
     * 供应商
     */
    private String supplierName;
    /**
     * 无货商品供应价
     */
    private BigDecimal noGoodsSupplyPrice;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 上下架 0上架1下架
     */
    private Integer onSale;
    /**
     * 库存
     */
    private Integer amount;
    /**
     * 变更库存
     */
    private Integer changeQuantity;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;
    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价
     */
    private Integer priceType;

    /**
     * 自有编码
     */
    private String itemCode;
    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * 默认价格
     */
    private MarketAreaItemMappingInput defaultPrice;
    /**
     * 门店价格
     */
    private List<MarketAreaItemMappingInput> storePrice;
    /**
     * 分组价格
     */
    private List<MarketAreaItemMappingInput> storeGroupPrice;

    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     *
     * @see com.cosfo.manage.common.context.GoodsTypeEnum
     */
    private Integer goodsType;
    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;
    /**
     * 防倒挂规则
     */
    private MarketItemUnfairPriceStrategyDTO marketItemUnfairPriceStrategyDTO;

    /**
     * 限购数量
     */
    private Integer saleLimitQuantity;

    /**
     * 限购规则
     */
    private Integer saleLimitRule;

    /**
     * 倍数订货 倍数值
     */
    @Min(value = 1, message = "货倍不能小于1")
    @Max(value = 10000, message = "货倍不能大于10000")
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;
    /**
     * 标准单价
     */
    private BigDecimal standardUnitPrice;
    /**
     * 管控门店库存0=不管;1=管控
     */
    private Boolean storeInventoryControlFlag;
    /**
     * 门店订货单位
     */
    private String storeOrderingUnit;
    /**
     * 成本 库存 单位倍数
     */
    private BigDecimal storeInventoryCostUnitMultiple;
    /**
     * 门店库存单位
     */
    private String storeInventoryUnit;
    /**
     * 订货 库存 单位倍数
     */
    private BigDecimal storeOrderingInventoryUnitMultiple;
    /**
     * 门店成本单位
     */
    private String storeCostUnit;

    /**
     * 无货商品重量
     */
    private BigDecimal weight;

    /**
     * sap sku编码
     */
    private String sapSkuCode;
    /**
     * sap 物料编码
     */
    private String sapMaterialCode;

    /**
     * 包装数量
     */
    private Integer packingQuantity;
    private String descriptionString;
}
