package com.cosfo.manage.market.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/31 10:51
 * @Description:
 */
@Data
public class MarketItemPriceStrategyVO implements Serializable {
    private static final long serialVersionUID = 8608009691295551781L;

    /**
     * item 主键
     */
    private Long itemId;

    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价
     * @see com.cosfo.manage.common.context.MarketAreaItemPriceTypeEnum
     */
    private Integer priceType;

    /**
     * 0 下架 1 上架
     */
    private Integer onSale;

    /**
     * 货源类型 0-无货商品 1-报价商品 2-自营货品
     * @see com.cosfo.manage.common.context.GoodsTypeEnum
     */
    private Integer goodsType;

    /**
     * 默认价格
     */
    private MarketAreaItemMappingVO defaultPrice;
    /**
     * 门店价格
     */
    private List<MarketAreaItemMappingVO> storePrice;
    /**
     * 分组价格
     */
    private List<MarketAreaItemMappingVO> storeGroupPrice;


    /**
     * 防倒挂策略
     */
    private MarketItemUnfairPriceStrategyVO marketItemUnfairPriceStrategyVO;
}
