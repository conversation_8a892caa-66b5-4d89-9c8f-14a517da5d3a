package com.cosfo.manage.market.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/2/15 15:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketItemQueryDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 商品编码 =id
     * /choose/page 中用的itemid
     * 后续严谨使用 统一用id字段
     */
    private Long itemId;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * market_id
     */
    private Long marketId;
    private List<Long> marketIds;

    /**
     * 货源类型
     *
     * @see com.cofso.item.client.enums.GoodsTypeEnum
     */
    private Integer goodsType;
    /**
     * 货品类型
     */
    private List<Integer> goodsTypes;
    /**
     * 查询非组合品
     * true-只查组合品 false-查询非组合品
     * 为空，查询所有Item
     */
    private Boolean combineFlag;
    /**
     * 上架状态
     */
    private Integer onSale;
    /**
     * 删除标识 0、已删除 1、正在使用
     */
    private Integer deleteFlag;
    /**
     * 页码
     */
    private Integer pageNum;
    private Integer pageIndex;

    /**
     * 分页数量
     */
    private Integer pageSize;

    /**
     * market item ids
     */
    private Collection<Long> itemIds;
    /**
     * 前台分类
     */
    private Long classificationId;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 后台类目
     */
    private List<Long> categoryIds;

    /**
     * 商品预售开关 0-不可预售 1-可预售
     */
    private Integer presaleSwitch;


    /**
     * sap商品编码 18位
     */
    private String sapSkuCode;

    /**
     * SAP物料编码 10位
     */
    private String sapMaterialCode;
}
