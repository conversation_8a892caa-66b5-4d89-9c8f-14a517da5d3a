package com.cosfo.manage.market.model.dto;

import com.cofso.item.client.req.MarketItemPriceInput;
import com.cosfo.manage.market.model.vo.MarketAreaItemMappingVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/4/28 14:04
 * @Description: market_item信息，与表结构保持基本一致
 */
@Data
public class MarketItemInfoDTO {


    /**
     * 商品item主键
     */
    private Long itemId;

    /**
     * tenant_id
     */
    private Long tenantId;

    /**
     * sku主键
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String itemTitle;

    /**
     * 商品头图
     */
    private String mainPicture;


    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 售价
     */
    private String priceStr;
    /**
     * 最大售价
     */
    private BigDecimal maxPrice;
    /**
     * 最小售价
     */
    private BigDecimal minPrice;


    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 产地
     */
    private String origin;

    /**
     * 商品主键
     */
    private Long marketId;

    /**
     * 品牌Id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 规格备注（区间）
     */
    private String weightNotes;

    /**
     * 供应商Id
     */
    private String supplierId;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 最大售后数
     */
    private Integer maxAfterSaleAmount;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 删除标识 0、已删除 1、正在使用
     */
    private Integer deleteFlag;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;
    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价 3组合品按总价下调固定额度,4组合品按总价下调百分比 5组合品总价
     *
     * @see com.cofso.item.client.enums.PriceTypeEnum
     */
    private Integer priceType;
    /**
     * 货品类型 0无货商品 1报价货品 2自营货品
     *
     * @see com.cofso.item.client.enums.GoodsTypeEnum
     */
    private Integer goodsType;
    /**
     * 0=实物商品,1=虚拟商品,2=组合品
     *
     * @see com.cofso.item.client.enums.ItemTypeEnum
     */
    private Integer itemType;
    /**
     * 0 下架 1 上架
     *
     * @see com.cofso.item.client.enums.OnSaleTypeEnum
     */
    private Integer onSale;
    /**
     * 库存数量（只有无货商品才有）
     */
    private Integer stockAmount;
    /**
     * 价格策略
     */
    private List<MarketAreaItemMappingVO> marketAreaItemMappingVos;
    /**
     * 价格策略
     */
    private List<MarketItemPriceInput> priceStrategyList;

    /**
     * 无货商品供应价
     */
    private BigDecimal noGoodsSupplyPrice;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;


    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;

    /**
     * 限购数量
     */
    private Integer saleLimitQuantity;

    /**
     * 限购规则 0不限制,1每次,2每自然日，3每自然周，4每自然月
     */
    private Integer saleLimitRule;
    /**
     * sap sku编码
     */
    private String sapSkuCode;
    /**
     * sap 物料编码
     */
    private String sapMaterialCode;

    /**
     * 包装数量
     */
    private Integer packingQuantity;
}
