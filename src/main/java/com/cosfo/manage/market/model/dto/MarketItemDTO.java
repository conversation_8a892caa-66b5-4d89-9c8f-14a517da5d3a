package com.cosfo.manage.market.model.dto;

import com.cosfo.manage.market.model.vo.MarketAreaItemMappingVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/15
 */
@Data
public class MarketItemDTO implements Serializable {
    /**
     * 主键Id
     */
    private Long id;
    /**
     * marketId
     */
    private Long marketId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * sku主键
     */
    private Long skuId;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格备注
     */
    private String weightNotes;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 0 下架 1 上架
     */
    private Integer onSale;
    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价
     */
    private Integer priceType;
    /**
     * 0 自营仓 1 第三方仓
     */
    @Deprecated
    private Integer warehouseType;
    /**
     * 配送方式 0品牌方配送 1三方配送
     */
    @Deprecated
    private Integer deliveryType;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;
//    /**
//     * 货品信息
//     */
//    private ProductSkuDTO productSkuDTO;
    /**
     * 价格策略
     */
    private List<MarketAreaItemMappingVO> marketAreaItemMappingVos;
    /**
     * 价格策略
     */
    private List<MarketItemPriceStrategyDTO> priceStrategyList;
    /**
     * 商城售卖价
     */
    private String priceStr;
    /**
     * 最小金额
     */
    private BigDecimal minPrice;
    /**
     * 最大金额
     */
    private BigDecimal maxPrice;
    /**
     * 库存信息
     */
    private Integer amount;
    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 供应商Id
     */
    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 无货商品供应价
     */
    private BigDecimal noGoodsSupplyPrice;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * 货源类型
     *
     * @see com.cosfo.manage.common.context.GoodsTypeEnum
     */
    private Integer goodsType;

    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;

    /**
     * marketItemUnfairPriceStrategyDTO
     */
    private MarketItemUnfairPriceStrategyDTO marketItemUnfairPriceStrategyDTO;

    /**
     * 限购数量
     */
    private Integer saleLimitQuantity;

    /**
     * 限购规则 0不限制,1每次,2每自然日，3每自然周，4每自然月
     */
    private Integer saleLimitRule;

    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;

    /**
     * 标准单价
     */
    private BigDecimal standardUnitPrice;
    /**
     * 管控门店库存0=不管;1=管控
     */
    private Boolean storeInventoryControlFlag;
    /**
     * 门店订货单位
     */
    private String storeOrderingUnit;
    /**
     * 成本 库存 单位倍数
     */
    private BigDecimal storeInventoryCostUnitMultiple;
    /**
     * 门店库存单位
     */
    private String storeInventoryUnit;
    /**
     * 订货 库存 单位倍数
     */
    private BigDecimal storeOrderingInventoryUnitMultiple;
    /**
     * 门店成本单位
     */
    private String storeCostUnit;

    /**
     * 无货商品重量
     */
    private BigDecimal weight;
    /**
     * sap sku编码
     */
    private String sapSkuCode;
    /**
     * sap 物料编码
     */
    private String sapMaterialCode;

    /**
     * 包装数量
     */
    private Integer packingQuantity;
}
