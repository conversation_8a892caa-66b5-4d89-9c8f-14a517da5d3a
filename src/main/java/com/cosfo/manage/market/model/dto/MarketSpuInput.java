package com.cosfo.manage.market.model.dto;

import lombok.Data;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/11
 */
@Data
public class MarketSpuInput {
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 主标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 分类
     */
    private Long classificationId;
    /**
     * 后台类目
     */
    private Long categoryId;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 商品描述 文字
     */
    private String descriptionString;
    /**
     * 商品项
     */
    private MarketItemInput marketItemInput;

    /**
     * 主要用来区别excel导入和新增
     */
    private Integer excelId;

    /**
     * 物料编码
     */
    private String sapMaterialCode;
    /**
     * SAP-SKU编码
     */
    private String sapSkuCode;
    /**
     * 包装数量
     */
    private Integer packingQuantity;
    private String classificationName;

    private String specificationUnit;

    private Integer miniOrderQuantity;
}
