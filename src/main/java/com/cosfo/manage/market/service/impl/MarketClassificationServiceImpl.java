package com.cosfo.manage.market.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cofso.item.client.req.MarketClassificationQueryReq;
import com.cofso.item.client.resp.MarketClassificationResp;
import com.cofso.item.client.resp.MarketClassificationTreeResp;
import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.common.util.transfer.MarketClassificationTreeTransferUtil;
import com.cosfo.manage.facade.MarketClassificationFacade;
import com.cosfo.manage.market.converter.MarketClassificationConvert;
import com.cosfo.manage.market.model.dto.MarketClassificationDTO;
import com.cosfo.manage.market.model.dto.MarketClassificationQueryDTO;
import com.cosfo.manage.market.model.dto.MarketClassificationTreeDTO;
import com.cosfo.manage.market.model.po.MarketClassification;
import com.cosfo.manage.market.model.vo.MarketClassificationTreeVO;
import com.cosfo.manage.market.model.vo.MarketClassificationVO;
import com.cosfo.manage.market.service.MarketClassificationService;
import com.google.common.collect.Lists;
import net.xianmu.common.exception.ParamsException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/10 9:51
 */
@Service
public class MarketClassificationServiceImpl implements MarketClassificationService {

    @Resource
    private MarketClassificationFacade marketClassificationFacade;

    /**
     * 一级分类父类ID
     */
    private static final Long DEFAULT_PRIMARY_ID = 0L;

    private static final String DEFAULT_ICON = "static/category-21.png";
    private static final String DEFAULT_NAME = "全部";
    private static final String DEFAULT_SECOND_NAME = "默认";

    @Override
    public ResultDTO saveClassification(MarketClassificationDTO classificationDTO, LoginContextInfoDTO contextInfoDTO) {
        if (Objects.isNull(contextInfoDTO) || Objects.isNull(contextInfoDTO.getTenantId())) {
            return ResultDTO.fail(ResultDTOEnum.MERCHANT_INFO_NOT_FOUND);
        }
        classificationDTO.setTenantId(contextInfoDTO.getTenantId());

        if (Objects.isNull(classificationDTO.getParentId()) && Objects.isNull(classificationDTO.getIcon())) {
            return ResultDTO.fail(ResultDTOEnum.PRIMARY_CLASSIFICATION_ICON_EMPTY);
        }

        Boolean flag = marketClassificationFacade.add(classificationDTO);
        return ResultDTO.success(flag);
    }

    @Override
    public Long createDefaultClassification(Long tenantId){
        Long firstClassificationId = getOrAddClassification(tenantId, DEFAULT_PRIMARY_ID, DEFAULT_NAME);
        Long secondClassificationId = getOrAddClassification(tenantId, firstClassificationId, DEFAULT_SECOND_NAME);
        return secondClassificationId;
    }

    private Long getOrAddClassification(Long tenantId, Long parentId, String name){
        MarketClassificationQueryReq req = new MarketClassificationQueryReq();
        req.setTenantId(tenantId);
        req.setParentId(parentId);
        req.setName(name);
        List<MarketClassificationResp> resp = marketClassificationFacade.queryClassificationByCondition(req);
        if(CollectionUtils.isEmpty(resp)){
            MarketClassificationDTO classificationDTO = new MarketClassificationDTO();
            classificationDTO.setTenantId(tenantId);
            classificationDTO.setName(name);
            classificationDTO.setParentId(parentId);
            if (Objects.equals(parentId, DEFAULT_PRIMARY_ID)) {
                classificationDTO.setIcon(DEFAULT_ICON);
            }
            marketClassificationFacade.add(classificationDTO);
            resp = marketClassificationFacade.queryClassificationByCondition(req);
        }
        return resp.get(0).getId();
    }

    @Override
    public ResultDTO selectClassificationTree(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            return ResultDTO.fail(ResultDTOEnum.MERCHANT_INFO_NOT_FOUND);
        }

        List<MarketClassificationTreeVO> marketClassificationTreeVOS = marketClassificationFacade.selectClassificationTree(tenantId);
        return ResultDTO.success(marketClassificationTreeVOS);
    }

    @Override
    public ResultDTO listAllById(Long id) {
        List<MarketClassificationTreeDTO> treeList = new ArrayList<>();
        MarketClassificationQueryReq marketClassificationQueryReq = new MarketClassificationQueryReq();
        marketClassificationQueryReq.setId(id);
        List<MarketClassificationResp> marketClassificationResps = marketClassificationFacade.queryClassificationByCondition(marketClassificationQueryReq);
        if(CollectionUtils.isEmpty(marketClassificationResps)){
            return ResultDTO.success();
        }

        MarketClassificationResp marketClassificationResp = marketClassificationResps.get(NumberConstant.ZERO);
        MarketClassification marketClassification = MarketClassificationConvert.convertTOMarketClassification(marketClassificationResp);
        if (!Objects.equals(marketClassification.getParentId(), DEFAULT_PRIMARY_ID)) {
            marketClassificationQueryReq.setId(marketClassification.getParentId());
            List<MarketClassificationResp> marketClassificationRespList = marketClassificationFacade.queryClassificationByCondition(marketClassificationQueryReq);
            MarketClassification primaryClassification = MarketClassificationConvert.convertTOMarketClassification(marketClassificationRespList.get(NumberConstant.ZERO));
            MarketClassificationTreeDTO treeDTO = MarketClassificationTreeTransferUtil.toMarketClassificationTreeDTO(primaryClassification);
            List<MarketClassification> childList = new ArrayList<>();
            childList.add(marketClassification);
            treeDTO.setChildList(childList);
            treeList.add(treeDTO);
            return ResultDTO.success(MarketClassificationTreeTransferUtil.toMarketClassificationTreeVO(treeList));
        }

        MarketClassificationTreeDTO treeDTO = MarketClassificationTreeTransferUtil.toMarketClassificationTreeDTO(marketClassification);
        marketClassificationQueryReq.setId(null);
        marketClassificationQueryReq.setParentId(marketClassification.getId());
        marketClassificationQueryReq.setTenantId(marketClassification.getTenantId());
        List<MarketClassificationResp> marketClassificationRespList = marketClassificationFacade.queryClassificationByCondition(marketClassificationQueryReq);
        if(!CollectionUtils.isEmpty(marketClassificationRespList)) {
            List<MarketClassification> childList = marketClassificationRespList.stream().map(MarketClassificationConvert::convertTOMarketClassification).collect(Collectors.toList());
            treeDTO.setChildList(childList);
            treeList.add(treeDTO);
        }

        return ResultDTO.success(MarketClassificationTreeTransferUtil.toMarketClassificationTreeVO(treeList));
    }

    @Override
    public ResultDTO deleteClassification(Long id, LoginContextInfoDTO contextInfoDTO) {
        if (Objects.isNull(contextInfoDTO) || Objects.isNull(contextInfoDTO.getTenantId())) {
            return ResultDTO.fail(ResultDTOEnum.MERCHANT_INFO_NOT_FOUND);
        }

        Boolean flag = marketClassificationFacade.delete(id, contextInfoDTO.getTenantId());
        return ResultDTO.success(flag);
    }

    @Override
    public ResultDTO updateClassification(MarketClassificationDTO marketClassificationDTO, LoginContextInfoDTO contextInfoDTO) {
        if (Objects.isNull(contextInfoDTO) || Objects.isNull(contextInfoDTO.getTenantId())) {
            return ResultDTO.fail(ResultDTOEnum.MERCHANT_INFO_NOT_FOUND);
        }

        marketClassificationDTO.setTenantId(contextInfoDTO.getTenantId());
        Boolean flag = marketClassificationFacade.update(marketClassificationDTO);
        return ResultDTO.success(flag);
    }

    @Override
    public ResultDTO sort(List<MarketClassificationTreeDTO> marketClassificationTreeDTOList, LoginContextInfoDTO loginContextInfoDTO) {
        if(CollectionUtils.isEmpty(marketClassificationTreeDTOList)){
            return ResultDTO.success();
        }

        for (MarketClassificationTreeDTO marketClassificationTreeDTO : marketClassificationTreeDTOList) {
            MarketClassificationDTO classificationDTO = new MarketClassificationDTO();
            classificationDTO.setId(marketClassificationTreeDTO.getId());
            classificationDTO.setTenantId(loginContextInfoDTO.getTenantId());
            classificationDTO.setSort(marketClassificationTreeDTO.getSort());
            marketClassificationFacade.update(classificationDTO);
            List<MarketClassification> childList = marketClassificationTreeDTO.getChildList();
            if (CollectionUtils.isEmpty(childList)) {
                continue;
            }

            for (MarketClassification marketClassification : childList) {
                MarketClassificationDTO marketClassificationDTO = new MarketClassificationDTO();
                marketClassificationDTO.setId(marketClassification.getId());
                marketClassificationDTO.setTenantId(loginContextInfoDTO.getTenantId());
                marketClassificationDTO.setSort(marketClassification.getSort());
                marketClassificationFacade.update(marketClassificationDTO);
            }
        }
        return ResultDTO.success();
    }

    @Override
    public MarketClassification selectByPrimaryKey(Long id) {
        MarketClassificationQueryReq req = new MarketClassificationQueryReq();
        req.setId(id);
        List<MarketClassificationResp> marketClassificationResps = marketClassificationFacade.queryClassificationByCondition(req);
        if(!CollectionUtils.isEmpty(marketClassificationResps)){
            return null;
        }

        MarketClassificationResp marketClassificationResp = marketClassificationResps.get(NumberConstant.ZERO);
        return MarketClassificationConvert.convertTOMarketClassification(marketClassificationResp);
    }

    @Override
    public MarketClassification selectByParentIdAndName(Long tenantId, Long parentId, String name) {
        MarketClassificationQueryReq req = new MarketClassificationQueryReq();
        req.setTenantId(tenantId);
        req.setParentId(parentId);
        req.setName(name);
        List<MarketClassificationResp> marketClassificationResps = marketClassificationFacade.queryClassificationByCondition(req);
        if(!CollectionUtils.isEmpty(marketClassificationResps)){
            return null;
        }

        MarketClassificationResp marketClassificationResp = marketClassificationResps.get(NumberConstant.ZERO);
        return MarketClassificationConvert.convertTOMarketClassification(marketClassificationResp);
    }

    @Override
    public MarketClassification selectByItemId(Long tenantId, Long marketId) {
        MarketClassificationQueryReq req = new MarketClassificationQueryReq();
        req.setTenantId(tenantId);
        req.setMarketId(marketId);
        List<MarketClassificationResp> marketClassificationResps = marketClassificationFacade.queryClassificationByCondition(req);
        if(!CollectionUtils.isEmpty(marketClassificationResps)){
            return null;
        }

        MarketClassificationResp marketClassificationResp = marketClassificationResps.get(NumberConstant.ZERO);
        return MarketClassificationConvert.convertTOMarketClassification(marketClassificationResp);
    }

    @Override
    public MarketClassificationDTO selectWholeClassification(Long tenantId, Long marketId) {
        if (Objects.isNull(tenantId) || Objects.isNull(marketId)) {
            throw new ParamsException("查询分组参数错误");
        }

        MarketClassificationResp marketClassificationResp = marketClassificationFacade.selectWholeClassification(tenantId, marketId);
        MarketClassificationDTO classificationDTO = MarketClassificationConvert.convertTOMarketClassificationDTO(marketClassificationResp);
        return classificationDTO;
    }

    @Override
    public List<MarketClassification> queryChildList(Long id) {
        AssertCheckParams.notNull(id, ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.getMessage());

        MarketClassificationQueryReq req = new MarketClassificationQueryReq();
        req.setId(id);
        List<MarketClassificationResp> marketClassificationResps = marketClassificationFacade.queryClassificationByCondition(req);
        if (CollectionUtils.isEmpty(marketClassificationResps)) {
            return Lists.newArrayList();
        }

        MarketClassificationResp marketClassificationResp = marketClassificationResps.get(NumberConstant.ZERO);
        MarketClassification marketClassification = MarketClassificationConvert.convertTOMarketClassification(marketClassificationResp);
        if (!Objects.equals(marketClassificationResp.getParentId(), DEFAULT_PRIMARY_ID)) {
            return Arrays.asList(marketClassification);
        }

        MarketClassificationQueryReq marketClassificationQueryReq = new MarketClassificationQueryReq();
        marketClassificationQueryReq.setTenantId(marketClassificationResp.getTenantId());
        marketClassificationQueryReq.setParentId(marketClassificationResp.getId());
        List<MarketClassificationResp> list = marketClassificationFacade.queryClassificationByCondition(marketClassificationQueryReq);
        // 二级分类查询
        if (CollectionUtils.isEmpty(list)) {
            return Arrays.asList(marketClassification);
        }

        List<MarketClassification> marketClassifications = list.stream().map(MarketClassificationConvert::convertTOMarketClassification).collect(Collectors.toList());
        return marketClassifications;
    }

    @Override
    public List<MarketClassificationVO> queryByClassificationIds(MarketClassificationQueryDTO marketClassificationQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        List<Long> classificationIds = marketClassificationQueryDTO.getClassificationIds();
        if(CollectionUtils.isEmpty(classificationIds)){
            return new ArrayList<>();
        }

        List<MarketClassificationTreeResp> marketClassificationTreeResps = marketClassificationFacade.queryByChildClassificationIds(classificationIds, loginContextInfoDTO.getTenantId());
        return MarketClassificationConvert.convertTOMarketClassificationVOList(marketClassificationTreeResps);
    }

    @Override
    public Map<String, Long> getClassificationMap(Long tenantId) {
        ResultDTO resultDTO = selectClassificationTree (tenantId);
        List<MarketClassificationTreeVO> classificationTreeVOList = (List<MarketClassificationTreeVO>) resultDTO.getData ();
        return new HashMap<>(classificationTreeVOList.stream()
                .filter(pc -> pc != null && pc.getChildList() != null && !pc.getChildList().isEmpty()) // 检查 pc 和 childList
                .flatMap(pc -> pc.getChildList().stream()
                        .filter(c -> c != null) // 检查 childList 中的每个元素
                        .map(c -> new AbstractMap.SimpleEntry<>(pc.getName() + "/" + c.getName(), c.getId())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

    }
}
