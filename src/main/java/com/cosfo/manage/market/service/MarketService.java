package com.cosfo.manage.market.service;

import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.market.model.dto.MarketAddResDTO;
import com.cosfo.manage.market.model.dto.MarketDeleteDTO;
import com.cosfo.manage.market.model.dto.MarketQueryInput;
import com.cosfo.manage.market.model.dto.MarketSpuInput;
import com.cosfo.manage.market.model.vo.MarketSpuVO;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/20
 */
public interface MarketService {
    /**
     * 新增
     *
     * @param marketSpuInput
     * @param tenantId
     * @return
     */
    MarketAddResDTO add(MarketSpuInput marketSpuInput, Long tenantId);

    /**
     * 更新
     *
     * @param marketSpuInput
     * @param tenantId
     * @return
     */
    void update(MarketSpuInput marketSpuInput, Long tenantId);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    MarketSpuVO detail(Long id);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    MarketSpuVO detailTenant(Long id,Long tenantId);

    /**
     * 分页查询
     *
     * @return
     */
    PageInfo<MarketSpuVO> list(MarketQueryInput marketQueryInput, LoginContextInfoDTO loginContextInfoDTO);


    /**
     * 导出商品
     * @param marketQueryDTO
     * @param loginContextInfoDTO
     */
    void export(MarketQueryInput marketQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 获取货源名称
     *
     * @param warehouseType
     * @param deliveryType
     * @return
     */
    String getOperationMode(Integer warehouseType,Integer deliveryType);

    /**
     * 删除商品
     * @param marketDeleteDTO
     */
    void delete(MarketDeleteDTO marketDeleteDTO);

    /**
     * 导入三方商品映射
     * @param file
     * @param tenantId
     * @return
     */
    ExcelImportResDTO importThirdMap(MultipartFile file, Long tenantId);

    String exportThirdMapTemplate();
}
