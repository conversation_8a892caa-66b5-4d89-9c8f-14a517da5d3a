package com.cosfo.manage.market.service;

import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.model.input.PageMarketItemInput;
import com.cosfo.manage.market.model.po.MarketItem;
import com.cosfo.manage.market.model.vo.MarketItemInfoPageVO;
import com.cosfo.manage.market.model.vo.MarketItemPageVo;
import com.cosfo.manage.market.model.vo.PriceStrategyRangeVO;
import com.cosfo.manage.market.model.vo.BatchOnSaleResultVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/18 11:12
 */
public interface MarketItemService {


    /**
     * 保存销售商品
     *
     * @param marketItemInput
     * @param tenantId
     */
    Long save(MarketItemInput marketItemInput, Long tenantId);

    /**
     * 更新销售商品
     *
     * @param marketItemInput
     * @param tenantId
     */
    void update(MarketItemInput marketItemInput, Long tenantId);

    /**
     * 查询商品详情
     *
     * @param itemId
     * @return
     */
    MarketItemDTO detail(Long tenantId,Long itemId);

    /**
     * 详情页商品列表
     *
     * @param marketItemQueryInput
     * @param loginContextInfoDTO
     * @return
     */
    List<MarketItemDTO> list(MarketItemQueryInput marketItemQueryInput, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 条件查询
     *
     * @param param
     * @return
     */
    List<MarketItemDTO> selectByParam(MarketItemQueryParam param);

    /**
     * 上下架
     *
     * @param marketItemInput
     * @param contextInfoDTO
     */
    CommonResult changOnSale(MarketItemOnSaleInput marketItemInput, LoginContextInfoDTO contextInfoDTO);

    /**
     * 批量上下架
     *  @param marketItemInput
     * @param contextInfoDTO
     * @return
     */
    BatchOnSaleResultVO batchChangOnSale(MarketItemOnSaleInput marketItemInput, LoginContextInfoDTO contextInfoDTO);

    /**
     * 根据货品Id查询关联销售商品信息
     *
     * @param skuIds
     * @param tenantId
     * @return
     */
    List<MarketItemDTO> queryBySkuIds(Collection<Long> skuIds, Long tenantId);

    /**
     * 根据market item ids 查询
     *
     * @param itemIds
     * @return
     */
    Map<Long, MarketItemDTO> getMapByItemIds(List<Long> itemIds);

    /**
     * 根据market items 查询
     * @param itemIds
     * @param tenantId
     * @return
     */
    Map<Long, MarketItemInfoDTO> getMapByItemIds(List<Long> itemIds, Long tenantId);

    /**
     * 删除item
     *
     * @param marketDeleteDTO
     */
    void delete(MarketDeleteDTO marketDeleteDTO);

    List<MarketItem> listAll(MarketItemQueryDTO marketItem);

    /**
     * 查询单个item的价格策略
     * @param itemId
     * @return
     */
    MarketItemDTO queryItemPriceStrategy(Long tenantId,Long itemId);

    /**
     * 修改单个item的价格策略
     *
     * @param input
     * @return
     */
    void updatePriceStrategy(MarketItemPriceStrategyInput input);

    /**
     * 浮动 批量更新价格策略值
     * @param tenantId
     * @param dto
     */
    void batchUpdatePriceStrategy(Long tenantId, PriceStrategyFloatingRangeDTO dto);
    /**
     * 批量查询 商品的价格策略  按照策略类型分组
     */
    PriceStrategyRangeVO batchQueryPriceStrategy(Long tenantId, List<Long> marketItemIds);

    /**
     * 查询最高成本价
     * @param tenantId
     * @param dto
     * @return
     */
    BigDecimal queryMaxCostPrice(Long tenantId, MaxCostPriceQueryDTO dto);
    /**
     * 批量导入 统一定价策略
     */
    ExcelImportResDTO importPriceStrategy(Long tenantId, MultipartFile file) throws IOException;

    /**
     * 批量导入 商品上下架
     * @param tenantId
     * @param file
     * @return
     */
    ExcelImportResDTO importOnSaleStrategy(Long tenantId, MultipartFile file) throws IOException;
    /**
     * excel批量修改无货商品成本价
     */
    ExcelImportResDTO importNoGoodsSupplyPrice(Long tenantId, MultipartFile file);
    /**
     * 修改商品自有编码
     */
    void updateItemCode(Long tenantId, ItemCodeUpdateDTO dto);

    /**
     * 分页查询条件结果
     *
     * @param queryDTO
     * @return
     */
    PageInfo<MarketItemInfoPageVO> queryMarketItemList(MarketItemQueryDTO queryDTO,MarketItemInfoQueryFlagDTO flagDTO);

    List<MarketItemInfoPageVO> queryMarketItemListWithUnit(Long tenantId, Set<Long> itemIds);

}
