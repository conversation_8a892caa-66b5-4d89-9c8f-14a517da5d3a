package com.cosfo.manage.market.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.enums.MarketItemUnfairPriceStrategyEnum;
import com.cosfo.common.excel.easyexcel.converter.EasyExcelLocalDateConverter;
import com.cosfo.common.excel.easyexcel.converter.LocalDateTimeConverter;
import com.cosfo.manage.common.config.GrayReleaseConfig;
import com.cosfo.manage.common.constant.Constants;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.constant.StringConstants;
import com.cosfo.manage.common.context.*;
import com.cosfo.manage.common.easy.excel.helper.ExcelMergeHandler;
import com.cosfo.manage.common.executor.ExecutorFactory;
import com.cosfo.manage.common.model.dto.DocCodeMappingDTO;
import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTOEnum;
import com.cosfo.manage.common.service.CommonService;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.common.util.ExcelUtils;
import com.cosfo.manage.common.util.PageInfoHelper;
import com.cosfo.manage.common.util.StringUtils;
import com.cosfo.manage.common.util.qiNiu.QiNiuUtils;
import com.cosfo.manage.facade.MarketFacade;
import com.cosfo.manage.facade.input.SupplierQueryInput;
import com.cosfo.manage.market.converter.MarketItemConverter;
import com.cosfo.manage.market.model.dto.*;
import com.cosfo.manage.market.model.vo.MarketItemCodeMapVO;
import com.cosfo.manage.market.model.vo.MarketItemExportVO;
import com.cosfo.manage.market.model.vo.MarketItemVO;
import com.cosfo.manage.market.model.vo.MarketSpuVO;
import com.cosfo.manage.market.service.MarketClassificationService;
import com.cosfo.manage.market.service.MarketItemService;
import com.cosfo.manage.market.service.MarketService;
import com.cosfo.manage.system.model.po.SystemParameters;
import com.cosfo.manage.system.service.SystemParametersService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/20
 */
@Slf4j
@Service
public class MarketServiceImpl implements MarketService {
    @Resource
    private MarketItemService marketItemService;
    @Resource
    private CommonService commonService;
    @Resource
    private MarketClassificationService marketClassificationService;
    @Resource
    private MarketFacade marketFacade;
    @Resource
    private GrayReleaseConfig grayReleaseConfig;
    @Resource
    private SystemParametersService systemParametersService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MarketAddResDTO add(MarketSpuInput marketSpuInput, Long tenantId) {
        marketSpuInput.setTenantId(tenantId);
        if (Objects.nonNull(marketSpuInput.getMarketItemInput())) {
            marketSpuInput.getMarketItemInput().setTenantId(tenantId);
        }
        marketSpuInput.setExcelId(0);
        checkLimitQuantityAndBuyMultiple(marketSpuInput.getMarketItemInput());
        return marketFacade.addMarket(marketSpuInput);
    }

    private void checkLimitQuantityAndBuyMultiple(MarketItemInput marketItemInput) {
        if(ObjectUtil.isEmpty (marketItemInput)){
            return;
        }
        if(Objects.equals (Boolean.TRUE,marketItemInput.getBuyMultipleSwitch ()) && Objects.equals (ItemSaleLimitRuleEnum.EVERY_TIME.getCode (),marketItemInput.getSaleLimitRule ())){
            Integer saleLimitQuantity = marketItemInput.getSaleLimitQuantity ();
            if(saleLimitQuantity < marketItemInput.getBuyMultiple ()){
                throw new BizException("訂貨倍數不可大於每次下單上限");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(MarketSpuInput marketSpuInput, Long tenantId) {
        AssertCheckParams.notNull(marketSpuInput.getId(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "id不能为空");
        marketSpuInput.setTenantId(tenantId);
        if (Objects.nonNull(marketSpuInput.getMarketItemInput())) {
            marketSpuInput.getMarketItemInput().setTenantId(tenantId);
        }
        marketFacade.updateMarket(marketSpuInput);
    }

    @Override
    public MarketSpuVO detail(Long id) {
        MarketSpuVO marketSpuVO = marketFacade.getMarketDetail(null, id);
        return marketSpuVO;
    }

    @Override
    public MarketSpuVO detailTenant(Long id,Long tenantId) {
        MarketSpuVO marketSpuVO = marketFacade.getMarketDetail(tenantId, id);
        return marketSpuVO;
    }

    @Override
    public PageInfo<MarketSpuVO> list(MarketQueryInput marketQueryInput, LoginContextInfoDTO loginContextInfoDTO) {
        if(Objects.equals (-1L ,  marketQueryInput.getSkuId ())){
            return PageInfo.emptyPageInfo ();
        }
        marketQueryInput.setTenantId(loginContextInfoDTO.getTenantId());
        if (!dealQueryCondition(marketQueryInput)) {
            return PageInfoHelper.createPageInfo(new ArrayList<>(), marketQueryInput.getPageSize());
        }
        PageInfo<MarketSpuVO> marketSpuVOPageInfo = marketFacade.queryMarketListPage(marketQueryInput);
//        Map<String, List<DocCodeMappingDTO>> docMapping =new HashMap<>();

        for (MarketSpuVO marketSpuVO : marketSpuVOPageInfo.getList()) {
            List<MarketItemVO> marketItemVOList = marketSpuVO.getMarketItemVOList ();
            if(CollectionUtil.isNotEmpty (marketItemVOList)) {
                marketItemVOList.stream ().filter (e -> Objects.equals (e.getSkuId (), -1L)).forEach (e -> e.setSkuId (null));
            }
//            fillDocMappingInfo(marketItemVOList, docMapping);
            marketSpuVO.setMarketItemVOList(marketItemVOList);
        }
        return marketSpuVOPageInfo;
    }

    private void fillDocMappingInfo(List<MarketItemVO> marketItemVOList, Map<String, List<DocCodeMappingDTO>> docCodeMapping) {
        if (CollectionUtils.isEmpty(marketItemVOList)) {
            return;
        }
        if (docCodeMapping == null || docCodeMapping.isEmpty()) {
            return;
        }
        for (MarketItemVO marketItemVO : marketItemVOList) {
            List<DocCodeMappingDTO> docCodeMappingDTO = docCodeMapping.get(String.valueOf(marketItemVO.getId()));
            // 需要合并 item 和 spec 相同 渠道的数据
            if (CollectionUtils.isEmpty(docCodeMappingDTO)) {
                return;
            }
            List<MarketItemCodeMapVO> itemCodeMappingList = docCodeMappingDTO.stream().map(dto -> {
                MarketItemCodeMapVO vo = new MarketItemCodeMapVO();
                vo.setChannelType(dto.getChannelType());
                vo.setThirdCode(dto.getOutCode());
                vo.setThirdSpecification(dto.getOutName());
                return vo;
            }).collect(Collectors.toList());

            marketItemVO.setItemCodeMap(itemCodeMappingList);
        }
    }

//    private Map<String, List<DocCodeMappingDTO>> queryDocMapping(Long tenantId, List<MarketSpuVO> marketSpuVOS) {
//        // 获取 marketItemId列表
//       List<String> itemIds = marketSpuVOS.stream()
//                .flatMap(spu -> spu.getMarketItemVOList() == null ? Stream.empty() : spu.getMarketItemVOList().stream())
//                .map(MarketItemVO::getId)
//                .filter(Objects::nonNull)
//                .distinct()
//                .map(String::valueOf)
//                .collect(Collectors.toList());
//        List<DocCodeMappingDTO> itemDocCodeMapping = docCodeMappingService.selectByTargetCodeAndType(itemIds, DocCodeTargetTypeEnum.MARKET_ITEM.getCode(), tenantId, null);
//        Map<String, List<DocCodeMappingDTO>> itemDocCodeMappingMap = itemDocCodeMapping.stream().collect(Collectors.groupingBy(DocCodeMappingDTO::getTargetCode));
//
//        return itemDocCodeMappingMap;
//    }

    private boolean dealQueryCondition(MarketQueryInput marketQueryInput) {
        // 销售商品查询字段
        marketQueryInput.setDeleteFlag(MarketDeleteFlagEnum.NORMAL.getFlag());
        return true;
    }

    @Override
    public void export(MarketQueryInput marketQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        marketQueryDTO.setTenantId(loginContextInfoDTO.getTenantId());
        //生成对应的查询条件
        Map<String, Object> queryParamsMap = new LinkedHashMap<>(NumberConstants.FIVE);
        if (!StringUtils.isBlank(marketQueryDTO.getTitle())) {
            queryParamsMap.put(Constants.TITLE, marketQueryDTO.getTitle());
        }
        if (Objects.nonNull(marketQueryDTO.getSkuId())) {
            queryParamsMap.put(Constants.SKU_ID_UPPER, marketQueryDTO.getSkuId());
        }
        if (!StringUtils.isBlank(marketQueryDTO.getBrandName())) {
            queryParamsMap.put(Constants.BRAND_NAME, marketQueryDTO.getBrandName());
        }
        if (Objects.nonNull(marketQueryDTO.getId())) {
            queryParamsMap.put(Constants.SPU, marketQueryDTO.getId().toString());
        }
        if (Objects.nonNull(marketQueryDTO.getItemId())) {
            queryParamsMap.put(Constants.ITEM_ID, marketQueryDTO.getItemId().toString());
        }
//        if (Objects.nonNull(marketQueryDTO.getClassificationId())) {
//            MarketClassification marketClassification = marketClassificationRepository.getById(marketQueryDTO.getClassificationId());
//            queryParamsMap.put(Constants.CLASSIFICATION_ID, marketClassification.getName());
//        }
        if (Objects.nonNull(marketQueryDTO.getItemCode())) {
            queryParamsMap.put(Constants.ITEM_CODE, marketQueryDTO.getItemCode().toString());
        }
        if (Objects.nonNull(marketQueryDTO.getOnSale())) {
            queryParamsMap.put(Constants.ON_SALE, OnSaleTypeEnum.getDesc(marketQueryDTO.getOnSale()));
        }
        if (Objects.nonNull(marketQueryDTO.getGoodsType())) {
            queryParamsMap.put(Constants.GOODS_TYPE, GoodsTypeEnum.getShowDescByCodeV2(marketQueryDTO.getGoodsType()));
        }
        if (Objects.nonNull(marketQueryDTO.getSupplierId())) {
            queryParamsMap.put(Constants.SUPPLIER_ID, getSupplierName(marketQueryDTO.getSupplierId(), loginContextInfoDTO.getTenantId()));
        }
        if (Objects.nonNull(marketQueryDTO.getUnfairPriceStrategyDefaultFlag())) {
            queryParamsMap.put(Constants.DEFAULT_FLAG_DESC, MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.getByCode(marketQueryDTO.getUnfairPriceStrategyDefaultFlag()).getDesc());
        }
        if (Objects.nonNull(marketQueryDTO.getUnfairPriceStrategyType())) {
            queryParamsMap.put(Constants.STRATEGY_TYPE_DESC, MarketItemUnfairPriceStrategyEnum.StrategyValueEnum.getByCode(marketQueryDTO.getUnfairPriceStrategyType()).getDesc());
        }


        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.SAAS);
        recordDTO.setBizType(FileDownloadTypeEnum.PRODUCT.getType());
        recordDTO.setTenantId(loginContextInfoDTO.getTenantId());
        recordDTO.setFileName(ExcelTypeEnum.PRODUCT.getFileName());
        recordDTO.setParams(queryParamsMap.isEmpty() ? Constants.TOTAL : JSONObject.toJSONString(queryParamsMap));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        DownloadCenterHelper.build(ExecutorFactory.generateExcelExecutor, recordDTO).asyncWriteWithOssResp(marketQueryDTO, e -> writeDownloadCenter(e, recordDTO.getFileName()));

    }


    public DownloadCenterOssRespDTO writeDownloadCenter(MarketQueryInput marketQueryInput, String fileName) {
        // 1、表格处理
        String filePath = generateMarketExportFile(marketQueryInput);

        // 2、文件上传至oss
        OssUploadResult uploadResult = null;
        try {
            uploadResult = OssUploadUtil.upload(fileName, FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
        } catch (IOException e) {
            log.error("filePath={}", filePath, e);
            throw new BizException("檔案讀取錯誤");
        } finally {
            commonService.deleteFile(filePath);
        }
        // 3、返回文件地址
        DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
        downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
        downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
        return downloadCenterOssRespDTO;
    }


    private String getSupplierName(Long supplierId, Long tenantId) {
        if(-1 == supplierId){
            return "杭州鲜沐科技有限公司";
        }
        SupplierQueryInput queryInput = new SupplierQueryInput();
        queryInput.setSupplierIds(Lists.newArrayList(supplierId));
        queryInput.setTenantId(tenantId);
        queryInput.setPageIndex(1);
        queryInput.setPageSize(10);
//        List<SupplierInfoDTO> supplierInfoDTOS = supplierFacade.batchQuerySupplier(queryInput);
//        if (CollectionUtils.isNotEmpty(supplierInfoDTOS)) {
//            return supplierInfoDTOS.get(0).getSupplierName();
//        }
        return String.valueOf(supplierId);
    }

    /**
     * 导出商品列表
     *
     * @param marketQueryInput
     */
    public String generateMarketExportFile(MarketQueryInput marketQueryInput) {
        // 写入excel
        // 从第几号开始合并
        int mergeRowIndex = 1;
        // 需要合并的列
        int[] mergeColumnIndex = {0, 1, 2, 3, 4, 5, 6, 7, 8};
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), ExcelTypeEnum.PRODUCT.getName());
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new EasyExcelLocalDateConverter()).registerWriteHandler(new ExcelMergeHandler(mergeRowIndex, mergeColumnIndex)).registerConverter(new LocalDateTimeConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();

        boolean flag = dealQueryCondition(marketQueryInput);
        if (!flag) {
            excelWriter.fill(Lists.newArrayList(), fillConfig, writeSheet);
            return filePath;
        }

        Integer pageSize = 100;
        marketQueryInput.setPageSize(pageSize);

        Integer serialNumber = NumberConstants.ZERO;

        int pageNo = 1;
        PageInfo<MarketSpuVO> pageMarketSpuVO = null;
        do {
            marketQueryInput.setPageIndex(pageNo++);
            pageMarketSpuVO = marketFacade.queryMarketListPage(marketQueryInput);
            if (pageMarketSpuVO == null || CollectionUtils.isEmpty(pageMarketSpuVO.getList())) {
                break;
            }
            List<MarketSpuVO> marketSpuVOS = pageMarketSpuVO.getList();
            List<Long> marketIds = pageMarketSpuVO.getList().stream().map(MarketSpuVO::getId).collect(Collectors.toList());

            List<MarketItemExportVO> exportList = new ArrayList<>();

            List<MarketItemDTO> marketItemDTOS = marketItemService.selectByParam(MarketItemQueryParam.builder()
                    .marketIds(marketIds)
                    .tenantId(marketQueryInput.getTenantId())
                    .build());
            marketItemDTOS = marketItemDTOS.stream().sorted(Comparator.comparing(MarketItemDTO::getMarketId)).collect(Collectors.toList());
            Map<Long, List<MarketItemDTO>> marketItemMap = marketItemDTOS.stream().collect(Collectors.groupingBy(MarketItemDTO::getMarketId));
            for (MarketSpuVO marketSpuVO : marketSpuVOS) {
                if (marketItemMap.containsKey(marketSpuVO.getId())) {
                    serialNumber++;
                    List<MarketItemDTO> itemDTOS = marketItemMap.get(marketSpuVO.getId());
                    for (MarketItemDTO marketItemDTO : itemDTOS) {
                        MarketItemExportVO marketItemExportVO = MarketItemConverter.convertToMarketItemExportVo(marketSpuVO, marketItemDTO);
                        if (marketItemExportVO != null) {
                            marketItemExportVO.setSerialNumber(serialNumber.toString());
                            exportList.add(marketItemExportVO);
                        }
                    }
                }
            }

            excelWriter.fill(exportList, fillConfig, writeSheet);

        } while (!pageMarketSpuVO.isIsLastPage());

        excelWriter.finish();
        return filePath;
    }

    @Override
    public String getOperationMode(Integer warehouseType, Integer GoodsType) {

        if (WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(warehouseType) || WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)) {
            return "自营货品-自营仓";
        } else if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType) && GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(GoodsType)) {
            return "自营货品-代仓";
        } else if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType) && GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(GoodsType)) {
            return "报价货品";
        }

        return "";
    }

    @Override
    public void delete(MarketDeleteDTO marketDeleteDTO) {
        marketFacade.deleteMarket(null, marketDeleteDTO.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ExcelImportResDTO importThirdMap(MultipartFile file, Long tenantId) {
        // 读excel
        List<MarketItemCodeMappingDTO> mappingDTOS;
        List<MarketItemCodeMappingDTO> errorStoreList = new ArrayList<>();
        ExcelImportResDTO resDTO = new ExcelImportResDTO();
        try {
            mappingDTOS = ExcelUtils.read(file.getInputStream(), MarketItemCodeMappingDTO.class);
        } catch (Exception e) {
            log.warn("POS ITEM excel 导入错误", e);
            throw new BizException("請確認EXCEL格式是否正確");
        }
        if (CollectionUtils.isEmpty(mappingDTOS)) {
                log.info("商品映射導入數據為空, tenantId={}", tenantId);
            throw new BizException("導入數據為空");
        }
        if (mappingDTOS.size() > 500) {
            throw new BizException("單次導入數據不能超過500條");
        }
        log.info("商品映射导入数据, tenantId={}, total={}", tenantId, mappingDTOS.size());
        List<Long> itemIds = mappingDTOS.stream()
                .filter(dto -> !StringUtils.isEmpty(dto.getMarketItemCode())
                        && StringUtils.isNumeric(dto.getMarketItemCode()))
                .map(mappingDTO -> Long.valueOf(mappingDTO.getMarketItemCode()))
                .collect(Collectors.toList());
        Map<Long, MarketItemInfoDTO> itemMap = marketItemService.getMapByItemIds(itemIds, tenantId);

        // channelType 只能是一种
        if (mappingDTOS.stream().map(MarketItemCodeMappingDTO::getPosChannel).distinct().count() > 1) {
            throw new BizException("單次導入僅支援一個渠道");
        }

        DocCodeChannelTypeEnum channelTypeEnum = DocCodeChannelTypeEnum.getByDesc(mappingDTOS.get(0).getPosChannel());
        if (channelTypeEnum == null) {
            throw new BizException("渠道類型不存在");
        }
        // 查询已经存在的映射关系
//        List<DocCodeMappingDTO> docCodeMappingDTOS = docCodeMappingService.selectByOutCodeAndType(mappingDTOS.stream().map(MarketItemCodeMappingDTO::getOutItemCode).collect(Collectors.toSet()), DocCodeTargetTypeEnum.MARKET_ITEM.getCode(), channelTypeEnum.getCode(), tenantId);
//        if (!CollectionUtils.isEmpty(docCodeMappingDTOS)) {
//            docCodeMappingService.deleteByIds(docCodeMappingDTOS.stream().map(DocCodeMappingDTO::getId).collect(Collectors.toList()));
//        }
        List<DocCodeMappingDTO> collect = new ArrayList<>(mappingDTOS.size());
        for (MarketItemCodeMappingDTO mappingDTO : mappingDTOS) {
            if (!mappingDTO.validateFields()) {
                errorStoreList.add(mappingDTO);
                continue;
            }
            MarketItemInfoDTO marketItemDTO = itemMap.get(Long.valueOf(mappingDTO.getMarketItemCode()));
            if (marketItemDTO == null) {
                mappingDTO.setErrorMsg("商品不存在");
                errorStoreList.add(mappingDTO);
                continue;
            }
            DocCodeMappingDTO marketMapping = new DocCodeMappingDTO();
            marketMapping.setTenantId(tenantId);
            marketMapping.setOutCode(mappingDTO.getOutItemCode());
            marketMapping.setTargetCode(mappingDTO.getMarketItemCode());
            marketMapping.setTargetType(DocCodeTargetTypeEnum.MARKET_ITEM.getCode());
            marketMapping.setChannelType(DocCodeChannelTypeEnum.getByDesc(mappingDTO.getPosChannel()).getCode());
            marketMapping.setOutName(mappingDTO.getOutItemSpecName());
            collect.add(marketMapping);
        }

        if (!org.springframework.util.CollectionUtils.isEmpty(collect)) {
//            docCodeMappingService.saveCodeMapping(collect);
        }
        resDTO.setFailRow(errorStoreList.size());
        resDTO.setSuccessRow(collect.size());
        // 异常数据写入excel
        if (errorStoreList.isEmpty()) {
            return resDTO;
        }
        String filePath = commonService.exportExcel(errorStoreList, ExcelTypeEnum.ERROR_POS_ITEM.getName());
        String qiNiuFilePath = null;
        try {
            qiNiuFilePath = QiNiuUtils.uploadFile(filePath, "三方POS商品导入错误信息" + UUID.randomUUID().toString().replaceAll(StringConstants.SEPARATING_IN_LINE, StringConstants.EMPTY) + ".xlsx");
        } catch (IOException e) {
            throw new BizException(e);
        }
        resDTO.setErrorFilePath(qiNiuFilePath);
        //删除临时文件
        commonService.deleteFile(filePath);
        return resDTO;
    }

    @Override
    public String exportThirdMapTemplate() {
        SystemParameters parameters = systemParametersService.selectByKey(Constants.MARKET_DOC_MAPPING_IMPORT_TEMPLATE_PATH);
        return parameters.getParamValue();
    }
}
