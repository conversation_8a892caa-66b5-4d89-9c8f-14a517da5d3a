package com.cosfo.manage.market.service;


import com.cosfo.manage.market.model.dto.MarketAreaItemMappingDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/18 11:18
 */
public interface MarketAreaItemMappingService {

    /**
     * 插入
     *
     * @param areaItemMapping
     */
    //void insert(MarketAreaItemMapping areaItemMapping);

    /**
     * 根据tenant和areaItemId查询
     *
     * @param tenantId
     * @param areaItemId
     * @return
     */
    //List<MarketAreaItemMapping> selectByAreaItemId(Long tenantId, Long areaItemId);

    /**
     * 删除门店策略
     *
     * @param id
     */
    void deleteByPrimaryKey(Long id, Long tenantId);

    /**
     * 更新
     *
     * @param updateMapping
     */
    //void updateByPrimaryKeySelective(MarketAreaItemMapping updateMapping);

    /**
     * 根据主键查询
     *
     * @param id
     * @return
     */
    //MarketAreaItemMapping selectById(Long id);

    /**
     * 查詢商品价格策略
     *
     * @param tenantId
     * @param areaItemId
     * @return
     */
    //List<MarketAreaItemMappingVO> queryByAreaItemId(Long tenantId, Long areaItemId, Long skuId);

    /**
     * 保存价格策略
     *
     * @param marketAreaItemMappingDtoList
     * @param tenantId
     */
    void save(List<MarketAreaItemMappingDTO> marketAreaItemMappingDtoList, Long tenantId, Long areaItemId);

    /**
     * 更新价格策略
     *
     * @param marketAreaItemMappingDtoList
     * @param tenantId
     * @param areaItemId
     */
    void update(List<MarketAreaItemMappingDTO> marketAreaItemMappingDtoList, Long tenantId, Long areaItemId);
}
