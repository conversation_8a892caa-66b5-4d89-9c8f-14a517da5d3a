package com.cosfo.manage.market.service.impl;

import cn.hutool.core.date.DateUtil;
import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.req.MarketItemOnSaleReq;
import com.cofso.item.client.req.MarketItemWithClassificationQueryReq;
import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;
import com.cofso.item.client.resp.MarketItemWithClassificationResp;
import com.cofso.page.PageResp;
import com.cosfo.manage.common.context.OnSaleTypeEnum;
import com.cosfo.manage.facade.Market4StoreFacade;
import com.cosfo.manage.market.service.MarketItemBusinessService;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import net.xianmu.common.exception.ParamsException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-05-25
 * @Description:
 */
@Service
public class MarketItemBusinessServiceImpl implements MarketItemBusinessService {

    @Resource
    private Market4StoreFacade market4StoreFacade;
    @DubboReference
    private MarketItemProvider marketItemProvider;
//    @Resource
//    private ProductMovementMapper productMovementMapper;


    @Override
    public Map<Long, List<MarketItemOnSaleSimple4StoreResp>> selectSaleMapBySkuIds(Long tenantId, List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_MAP;
        }
        MarketItemOnSaleReq marketItemOnSaleReq = new MarketItemOnSaleReq();
        marketItemOnSaleReq.setTenantId(tenantId);
        marketItemOnSaleReq.setSkuIds(skuIds);
        List<MarketItemOnSaleSimple4StoreResp> marketItemOnSaleSimple4StoreRespList = market4StoreFacade.queryMarketItemOnSaleInfo(marketItemOnSaleReq);
        return marketItemOnSaleSimple4StoreRespList.stream().collect(Collectors.groupingBy(MarketItemOnSaleSimple4StoreResp::getSkuId));
    }

    @Override
    public List<MarketItemOnSaleSimple4StoreResp> selectSaleStatusBySkuIds(Long tenantId, List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_LIST;
        }
        MarketItemOnSaleReq marketItemOnSaleReq = new MarketItemOnSaleReq();
        marketItemOnSaleReq.setTenantId(tenantId);
        marketItemOnSaleReq.setSkuIds(skuIds);
        return market4StoreFacade.queryMarketItemOnSaleInfo(marketItemOnSaleReq);
    }

    @Override
    public List<Long> queryAllItemIds(Long tenantId) {
        List<Long> allItemIds = new ArrayList<>();
        MarketItemWithClassificationQueryReq req = new MarketItemWithClassificationQueryReq();
        req.setTenantId(tenantId);
        req.setPageSize(100);
        req.setPageIndex(1);
        while (true) {
            PageResp<MarketItemWithClassificationResp> page = RpcResultUtil.handle(marketItemProvider.queryItemWithClassification(req));
            if (CollectionUtils.isEmpty(page.getList())) {
                break;
            }
            allItemIds.addAll(page.getList().stream().map(MarketItemWithClassificationResp::getId).collect(Collectors.toList()));
            req.setPageIndex(page.getPageNum() + 1);
        }
        return allItemIds;
    }

    @Override
    public long selectOnSaleMarketItemCounts(Long tenantId, String dateTime) {
        if (tenantId == null) {
            throw new ParamsException("租户id不能为空");
        }

        Date timeDate = DateUtil.parse(dateTime);
        boolean isToday = DateUtil.isSameDay(timeDate, DateUtil.date());
        if (!isToday) {
            // 非今天查询离线数据
//            ProductMovementQueryDTO query = ProductMovementQueryDTO.builder().tenantId(UserLoginContextUtils.getTenantId()).timeTag(DateUtil.yesterday().toString("yyyyMMdd")).type(TimeDimensionEnum.DAY.getType()).build();
//            ProductMovement productMovement = productMovementMapper.querySummary(query);
//            return Objects.isNull(productMovement) ? 0 : productMovement.getOnSaleNum();
        }

        MarketItemOnSaleReq marketItemOnSaleReq = new MarketItemOnSaleReq();
        marketItemOnSaleReq.setTenantId(tenantId);
        marketItemOnSaleReq.setOnSale(OnSaleTypeEnum.ON_SALE.getCode());
        List<MarketItemOnSaleSimple4StoreResp> marketItemOnSaleSimple4StoreResps = market4StoreFacade.queryOnSaleMarketItems(marketItemOnSaleReq);
        if (CollectionUtils.isEmpty(marketItemOnSaleSimple4StoreResps)) {
            return 0L;
        }
        return marketItemOnSaleSimple4StoreResps.stream().map(MarketItemOnSaleSimple4StoreResp::getId).distinct().count();
    }
}
