package com.cosfo.manage.market.service;

import com.cosfo.manage.market.model.dto.MarketAreaItemDTO;
import com.cosfo.manage.market.model.dto.MarketItemInput;
import com.cosfo.manage.market.model.po.MarketAreaItem;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/18 11:16
 */
// 商品中心迁移，未使用到当前类
public interface MarketAreaItemService {

    /**
     * 插入
     * @param areaItem
     */
    void insert(MarketAreaItem areaItem);

    /**
     * 根据tenant和sku查询
     * @param tenantId
     * @param skuId
     * @return
     */
    MarketAreaItem selectByTenantAndSkuId(Long tenantId, Long skuId);

    /**
     * 更新
     * @param update
     */
    void updateByPrimaryKeySelective(MarketAreaItem update);

    /**
     * 根据租户Id和skuId查询
     *
     * @param tenantId
     * @param skuId
     * @return
     */
    MarketAreaItemDTO queryByTenantIdAndSkuId(Long tenantId, Long skuId);

}
