package com.cosfo.manage.market.service.impl;

import com.cosfo.manage.market.mapper.MarketAreaItemMappingMapper;
import com.cosfo.manage.market.model.dto.MarketAreaItemMappingDTO;
import com.cosfo.manage.market.model.po.MarketAreaItemMapping;
import com.cosfo.manage.market.model.po.MarketAreaItemStorePriceMapping;
import com.cosfo.manage.market.repository.MarketAreaItemStorePriceMappingRepository;
import com.cosfo.manage.market.service.MarketAreaItemMappingService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/18 11:19
 */
@Service
public class MarketAreaItemMappingServiceImpl implements MarketAreaItemMappingService {

    @Resource
    private MarketAreaItemMappingMapper marketAreaItemMappingMapper;
    @Resource
    private MarketAreaItemStorePriceMappingRepository marketAreaItemStorePriceMappingRepository;
//    @Resource
//    @Lazy
//    private MerchantStoreService merchantStoreService;
//    @Resource
//    private MerchantStoreGroupMappingRepository merchantStoreGroupMappingRepository;
//    @Resource
//    private ProductPricingSupplyService productPricingSupplyService;
//    @Resource
//    private MerchantAddressMapper merchantAddressMapper;
//    @Resource
//    private MerchantAddressService merchantAddressService;


    /*@Override
    public void insert(MarketAreaItemMapping areaItemMapping) {
        marketAreaItemMappingMapper.insert(areaItemMapping);
    }
*/
    /*@Override
    public List<MarketAreaItemMapping> selectByAreaItemId(Long tenantId, Long areaItemId) {
        return marketAreaItemMappingMapper.selectByAreaItemId(tenantId, areaItemId);
    }
*/
    @Override
    public void deleteByPrimaryKey(Long id, Long tenantId) {
        // 删除门店关联
        marketAreaItemStorePriceMappingRepository.deleteByAreaItemMappingId(id, tenantId);
        // 删除价格策略
        marketAreaItemMappingMapper.deleteByPrimaryKey(id);
    }

    /*@Override
    public void updateByPrimaryKeySelective(MarketAreaItemMapping updateMapping) {
        marketAreaItemMappingMapper.updateByPrimaryKeySelective(updateMapping);
    }
*/
    /*@Override
    public MarketAreaItemMapping selectById(Long id) {
        return marketAreaItemMappingMapper.selectByPrimaryKey(id);
    }
*/
    /*@Override
    public List<MarketAreaItemMappingVO> queryByAreaItemId(Long tenantId, Long areaItemId, Long skuId) {
        List<MarketAreaItemMapping> marketAreaItemMappings = selectByAreaItemId(tenantId, areaItemId);
        List<MarketAreaItemMappingVO> marketAreaItemMappingVos = new ArrayList<>(NumberConstant.TEN);
        if (CollectionUtils.isEmpty(marketAreaItemMappings)) {
            return marketAreaItemMappingVos;
        }

        List<Long> areaItemMappingIds = marketAreaItemMappings.stream().map(MarketAreaItemMapping::getId).collect(Collectors.toList());
        // 查询所有关联门店
        List<MarketAreaItemStorePriceMapping> marketAreaItemStorePriceMappings = marketAreaItemStorePriceMappingRepository.batchQuery(areaItemMappingIds, tenantId);
        if (!CollectionUtils.isEmpty(marketAreaItemMappings)) {
            Map<Long, List<MarketAreaItemStorePriceMapping>> map = new HashMap<>(NumberConstant.SIXTEEN);
            Map<Long, MerchantStoreDTO> merchantStoreDtoMap = new HashMap<>(NumberConstant.SIXTEEN);
            Map<Long, MerchantStoreGroupMapping> merchantStoreGroupMappingMap = new HashMap<>(NumberConstant.SIXTEEN);
            List<String> cityNames = new ArrayList<>(NumberConstants.TEN);
            Map<Long, MerchantDeliveryAddressResultResp> merchantAddressDtoMap = new HashMap<>(NumberConstant.SIXTEEN);
            if (!CollectionUtils.isEmpty(marketAreaItemStorePriceMappings)) {
                // 按价格统计
                map = marketAreaItemStorePriceMappings.stream().collect(Collectors.groupingBy(MarketAreaItemStorePriceMapping::getAreaItemMappingId));
                // 查询门店信息
                List<Long> storeIds = marketAreaItemStorePriceMappings.stream().map(MarketAreaItemStorePriceMapping::getStoreId).collect(Collectors.toList());
                List<MerchantStoreDTO> merchantStoreDtos = merchantStoreService.batchQueryByStoreIds(storeIds, tenantId);
                merchantStoreDtoMap = merchantStoreDtos.stream().collect(Collectors.toMap(MerchantStoreDTO::getId, item -> item));
                // 查询分组
                List<MerchantStoreGroupMapping> merchantStoreGroupMappingList = merchantStoreGroupMappingRepository.selectByStoreIds(storeIds, tenantId);
                if (!CollectionUtils.isEmpty(merchantStoreGroupMappingList)) {
                    merchantStoreGroupMappingMap = merchantStoreGroupMappingList.stream().collect(Collectors.toMap(MerchantStoreGroupMapping::getStoreId, item -> item));
                }

                // 查询供应状态
                List<ProductPricingSupplyCityMappingDTO> productPricingSupplyCityMappingDTOS = productPricingSupplyService.querySupplyCityBySkuId(skuId, tenantId);
                if (!CollectionUtils.isEmpty(productPricingSupplyCityMappingDTOS)) {
                    cityNames = productPricingSupplyCityMappingDTOS.stream().map(ProductPricingSupplyCityMappingDTO::getCityName).collect(Collectors.toList());
                }

                // 获取地址
                List<MerchantDeliveryAddressResultResp> merchantAddressDTOS = merchantAddressService.selectByStoreIds(tenantId, storeIds);
                merchantAddressDtoMap = merchantAddressDTOS.stream().collect(Collectors.toMap(MerchantDeliveryAddressResultResp::getStoreId, item -> item));
            }

            Map<Long, List<MarketAreaItemStorePriceMapping>> finalMap = map;
            Map<Long, MerchantStoreDTO> finalMerchantStoreDtoMap = merchantStoreDtoMap;
            Map<Long, MerchantStoreGroupMapping> finalMerchantStoreGroupMappingMap = merchantStoreGroupMappingMap;
            List<String> finalCityNames = cityNames;
            Map<Long, MerchantDeliveryAddressResultResp> finalMerchantAddressDtoMap = merchantAddressDtoMap;
            marketAreaItemMappings.forEach(marketAreaItemMapping -> {
                MarketAreaItemMappingVO marketAreaItemMappingVo = new MarketAreaItemMappingVO();
                marketAreaItemMappingVos.add(marketAreaItemMappingVo);
                BeanUtils.copyProperties(marketAreaItemMapping, marketAreaItemMappingVo);
                // 绑定门店
                List<MarketAreaItemStorePriceMapping> list = finalMap.get(marketAreaItemMappingVo.getId());
                if (!CollectionUtils.isEmpty(list)) {
                    List<MerchantStoreDTO> merchantStoreDtoList = list.stream().map(marketAreaItemStorePriceMapping -> {
                        MerchantStoreDTO merchantStoreDto = finalMerchantStoreDtoMap.get(marketAreaItemStorePriceMapping.getStoreId());
                        merchantStoreDto.setAreaItemMappingId(marketAreaItemMapping.getId());
                        // 门店地址
                        if (finalMerchantAddressDtoMap.containsKey(merchantStoreDto.getId())) {
                            MerchantDeliveryAddressResultResp merchantAddressDto = finalMerchantAddressDtoMap.get(merchantStoreDto.getId());
                            merchantStoreDto.setProvince(merchantAddressDto.getProvince());
                            merchantStoreDto.setCity(merchantAddressDto.getCity());
                            merchantStoreDto.setArea(merchantAddressDto.getArea());
                            merchantStoreDto.setAddress(merchantAddressDto.getAddress());
                            merchantStoreDto.setHouseNumber(Objects.isNull(merchantAddressDto.getHouseNumber()) ? "" : merchantAddressDto.getHouseNumber());
                            merchantStoreDto.setContactName(merchantAddressDto.getContactName());
                            merchantStoreDto.setDeliveryAddress(merchantAddressDto.getDeliveryAddress());
                            merchantStoreDto.setContactPhone(merchantAddressDto.getContactPhone());
                        }

                        // 门店分组
                        if (finalMerchantStoreGroupMappingMap.containsKey(merchantStoreDto.getId())) {
                            MerchantStoreGroupMapping merchantStoreGroupMapping = finalMerchantStoreGroupMappingMap.get(merchantStoreDto.getId());
                            merchantStoreDto.setGroupId(merchantStoreGroupMapping.getGroupId());
                        }

                        merchantStoreDto.setSupplyStatus(finalCityNames.contains(merchantStoreDto.getCity()) ? NumberConstants.ZERO : NumberConstants.ONE);
                        return merchantStoreDto;
                    }).collect(Collectors.toList());
                    marketAreaItemMappingVo.setMerchantStoreDtoList(merchantStoreDtoList);
                }
            });
        }

        return marketAreaItemMappingVos;
    }*/

    @Override
    public void save(List<MarketAreaItemMappingDTO> marketAreaItemMappingDtoList, Long tenantId, Long areaItemId) {
        marketAreaItemMappingDtoList.forEach(marketAreaItemMappingDTO -> {
            MarketAreaItemMapping marketAreaItemMapping = MarketAreaItemMapping.builder().tenantId(tenantId).areaItemId(areaItemId).storePriceType(marketAreaItemMappingDTO.getStorePriceType()).type(marketAreaItemMappingDTO.getType()).mappingNumber(marketAreaItemMappingDTO.getMappingNumber()).build();
            marketAreaItemMappingMapper.insertSelective(marketAreaItemMapping);
            if (!CollectionUtils.isEmpty(marketAreaItemMappingDTO.getStoreIds())) {
                List<MarketAreaItemStorePriceMapping> marketAreaItemStorePriceMappings = marketAreaItemMappingDTO.getStoreIds().stream().map(storeId -> {
                    MarketAreaItemStorePriceMapping marketAreaItemStorePriceMapping = new MarketAreaItemStorePriceMapping();
                    marketAreaItemStorePriceMapping.setTenantId(tenantId);
                    marketAreaItemStorePriceMapping.setStoreId(storeId);
                    marketAreaItemStorePriceMapping.setAreaItemMappingId(marketAreaItemMapping.getId());
                    return marketAreaItemStorePriceMapping;
                }).collect(Collectors.toList());
                marketAreaItemStorePriceMappingRepository.saveBatch(marketAreaItemStorePriceMappings);
            }
        });
    }

    @Override
    public void update(List<MarketAreaItemMappingDTO> marketAreaItemMappingDtoList, Long tenantId, Long areaItemId) {
        // 查询已有价格策略
        List<MarketAreaItemMapping> marketAreaItemMappings = marketAreaItemMappingMapper.selectByAreaItemId(tenantId, areaItemId);
        // 新的
        List<Long> areaItemMappingIds = marketAreaItemMappingDtoList.stream().map(MarketAreaItemMappingDTO::getId).collect(Collectors.toList());
        // 需要删除的
        List<MarketAreaItemMapping> needDelete = marketAreaItemMappings.stream().filter(item -> !areaItemMappingIds.contains(item.getId())).collect(Collectors.toList());
        needDelete.forEach(marketAreaItemMapping -> {
            deleteByPrimaryKey(marketAreaItemMapping.getId(), tenantId);
        });

        marketAreaItemMappingDtoList.forEach(marketAreaItemMappingDto -> {
            // 新增
            if (Objects.isNull(marketAreaItemMappingDto.getId())) {
                MarketAreaItemMapping marketAreaItemMapping = MarketAreaItemMapping.builder().tenantId(tenantId).areaItemId(areaItemId).storePriceType(marketAreaItemMappingDto.getStorePriceType()).type(marketAreaItemMappingDto.getType()).mappingNumber(marketAreaItemMappingDto.getMappingNumber()).build();
                marketAreaItemMappingMapper.insertSelective(marketAreaItemMapping);
                // 绑定门店
                if (!CollectionUtils.isEmpty(marketAreaItemMappingDto.getStoreIds())) {
                    List<MarketAreaItemStorePriceMapping> marketAreaItemStorePriceMappings = marketAreaItemMappingDto.getStoreIds().stream().map(storeId -> {
                        MarketAreaItemStorePriceMapping marketAreaItemStorePriceMapping = new MarketAreaItemStorePriceMapping();
                        marketAreaItemStorePriceMapping.setTenantId(tenantId);
                        marketAreaItemStorePriceMapping.setStoreId(storeId);
                        marketAreaItemStorePriceMapping.setAreaItemMappingId(marketAreaItemMapping.getId());
                        return marketAreaItemStorePriceMapping;
                    }).collect(Collectors.toList());
                    marketAreaItemStorePriceMappingRepository.saveBatch(marketAreaItemStorePriceMappings);
                }
            // 修改
            }else {
                MarketAreaItemMapping marketAreaItemMapping = MarketAreaItemMapping.builder().id(marketAreaItemMappingDto.getId()).tenantId(tenantId).areaItemId(areaItemId).storePriceType(marketAreaItemMappingDto.getStorePriceType()).type(marketAreaItemMappingDto.getType()).mappingNumber(marketAreaItemMappingDto.getMappingNumber()).build();
                marketAreaItemMappingMapper.updateByPrimaryKeySelective(marketAreaItemMapping);
                // 查询已有门店信息
                List<MarketAreaItemStorePriceMapping> marketAreaItemStorePriceMappings = marketAreaItemStorePriceMappingRepository.batchQuery(Arrays.asList(marketAreaItemMappingDto.getId()), tenantId);
                // 判断已绑定门店和新增门店
                List<Long> oldStoreIds = marketAreaItemStorePriceMappings.stream().map(MarketAreaItemStorePriceMapping::getStoreId).collect(Collectors.toList());
                // 绑定门店
                if (!CollectionUtils.isEmpty(marketAreaItemMappingDto.getStoreIds())) {
                    List<Long> storeIds = marketAreaItemMappingDto.getStoreIds();
                    // 需要删除的
                    List<Long> needDeleteStoreIds = oldStoreIds.stream().filter(item -> !storeIds.contains(item)).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(needDeleteStoreIds)) {
                        marketAreaItemStorePriceMappingRepository.batchDelete(needDeleteStoreIds, tenantId);
                    }
                    // 需要新增的
                    List<Long> needAddStoreIds = storeIds.stream().filter(item -> !oldStoreIds.contains(item)).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(needAddStoreIds)) {
                        List<MarketAreaItemStorePriceMapping> list = needAddStoreIds.stream().map(storeId -> {
                            MarketAreaItemStorePriceMapping marketAreaItemStorePriceMapping = new MarketAreaItemStorePriceMapping();
                            marketAreaItemStorePriceMapping.setTenantId(tenantId);
                            marketAreaItemStorePriceMapping.setStoreId(storeId);
                            marketAreaItemStorePriceMapping.setAreaItemMappingId(marketAreaItemMapping.getId());
                            return marketAreaItemStorePriceMapping;
                        }).collect(Collectors.toList());
                        marketAreaItemStorePriceMappingRepository.saveBatch(list);
                    }
                }else {
                    // 全部删除
                    marketAreaItemStorePriceMappingRepository.deleteByAreaItemMappingId(marketAreaItemMapping.getId(), tenantId);
                }
            }
        });
    }
}
