package com.cosfo.manage.msg.service.impl;

import com.cosfo.manage.common.constant.NumberConstant;
import com.cosfo.manage.common.constant.NumberConstants;
import com.cosfo.manage.common.context.TenantAccountEnums;
import com.cosfo.manage.common.context.TenantConfigEnum;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.util.AssertCheckParams;
import com.cosfo.manage.common.util.PageInfoConverter;
import com.cosfo.manage.facade.AuthUserFacade;
import com.cosfo.manage.facade.SendLogFacade;
import com.cosfo.manage.msg.convert.SendLogConvert;
import com.cosfo.manage.msg.model.req.MessageUnReadDTO;
import com.cosfo.manage.msg.model.req.MsgTipMarkDTO;
import com.cosfo.manage.msg.model.req.MessageTipPageDTO;
import com.cosfo.manage.msg.model.resp.MsgTipVO;
import com.cosfo.manage.msg.service.SendLogService;
import com.cosfo.manage.tenant.model.po.TenantCommonConfig;
import com.cosfo.manage.tenant.service.TenantCommonConfigService;
import com.cosfo.message.client.common.page.req.PageQueryReq;
import com.cosfo.message.client.common.page.resp.PageResp;
import com.cosfo.message.client.enums.MessageContentTypeEnum;
import com.cosfo.message.client.enums.ReadStatusEnum;
import com.cosfo.message.client.req.BatchMarkMsgReq;
import com.cosfo.message.client.req.MsgSendLogQueryReq;
import com.cosfo.message.client.req.TenantNotifyReq;
import com.cosfo.message.client.resp.MsgNotifySendLogResp;
import com.cosfo.message.client.resp.MsgSendLogResp;
import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthUserRoleDto;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2023-07-18
 * @Description:
 */
@Service
public class SendLogServiceImpl implements SendLogService {

    @Resource
    private TenantCommonConfigService tenantCommonConfigService;
    @Resource
    private AuthUserFacade authUserFacade;
    @Resource
    private SendLogFacade sendLogFacade;

    private Boolean checkSupplierRole(Long tenantId,Long authUserId) {
        TenantCommonConfig tenantCommonConfig = tenantCommonConfigService.selectByTenantIdAndConfigKey(tenantId, TenantConfigEnum.TenantConfig.SUPPLIER_DISTRIBUTOR_ROLE_ID.getConfigKey());
        if (tenantCommonConfig != null) {
            List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Collections.singletonList(authUserId));
            Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
            AuthUserRoleDto authUserRoleDto = authUserRoleDtoMap.get(authUserId);
            if (Objects.nonNull(authUserRoleDto)) {
                List<AuthRole> roles = authUserRoleDto.getRoles();
                // 判断是否是供应商
                return roles.stream().anyMatch(role -> tenantCommonConfig.getConfigValue ().equals(String.valueOf (role.getId ())));
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public PageInfo<MsgTipVO> page(MessageTipPageDTO messageTipPageDTO, LoginContextInfoDTO merchantInfoDTO) {
        if(checkSupplierRole(merchantInfoDTO.getTenantId (),merchantInfoDTO.getAuthUserId())){
            return PageInfo.emptyPageInfo ();
        }
        MsgSendLogQueryReq msgSendLogQueryReq = SendLogConvert.messageTipPageDTO2Req(messageTipPageDTO, merchantInfoDTO);
        ReadStatusEnum readStatusEnum = ReadStatusEnum.getByStatus(messageTipPageDTO.getReadStatus());
        // 非未读消息列表组装查询时间
        if (ReadStatusEnum.UN_READ != readStatusEnum) {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusMonths(NumberConstants.SIX);
            msgSendLogQueryReq.setStartTime(startTime);
            msgSendLogQueryReq.setEndTime(endTime);
        }

        PageQueryReq pageQueryReq = new PageQueryReq();
        pageQueryReq.setPageIndex(messageTipPageDTO.getPageIndex());
        pageQueryReq.setPageSize(messageTipPageDTO.getPageSize());
        PageResp<MsgNotifySendLogResp> pageResp = sendLogFacade.page4Manage(msgSendLogQueryReq, pageQueryReq);

        List<MsgTipVO> msgTipVOList = pageResp.getData().stream().map(SendLogConvert::sendLogRespToVO).collect(Collectors.toList());
        PageInfo<MsgTipVO> msgSendLogRespPageInfo = PageInfoConverter.pageRespToPageInfo(pageResp, msgTipVOList);
        return msgSendLogRespPageInfo;
    }

    @Override
    public Integer countUnRead(MessageUnReadDTO messageUnReadDTO, LoginContextInfoDTO merchantInfoDTO) {
        if(checkSupplierRole(merchantInfoDTO.getTenantId (),merchantInfoDTO.getAuthUserId())){
            return 0;
        }
        MsgSendLogQueryReq msgSendLogQueryReq = MsgSendLogQueryReq.builder()
                .tenantId(merchantInfoDTO.getTenantId())
                .readStatusEnum(ReadStatusEnum.UN_READ).build();
        return sendLogFacade.countByCondition(msgSendLogQueryReq);
    }

    @Override
    public void markBatch(MsgTipMarkDTO msgTipMarkDTO, LoginContextInfoDTO merchantInfoDTO) {
        if(checkSupplierRole(merchantInfoDTO.getTenantId (),merchantInfoDTO.getAuthUserId())){
            throw new BizException("無權限");
        }
        ReadStatusEnum readStatusEnum = ReadStatusEnum.getByStatus(msgTipMarkDTO.getReadStatus());
        AssertCheckParams.expectTrue(Objects.nonNull(readStatusEnum), ResultStatusEnum.SERVER_ERROR.getStatus(), "readStatus参数异常");

        // 批量已读不传contentType,若后续存在单个消息类型批量已读再扩展
        BatchMarkMsgReq batchMarkMsgReq = BatchMarkMsgReq.builder()
                .ids(msgTipMarkDTO.getIds())
                .tenantId(merchantInfoDTO.getTenantId())
                .readStatusEnum(readStatusEnum).build();
        sendLogFacade.markBatch(batchMarkMsgReq);
    }

    @Override
    public List<MsgTipVO> warnList(LoginContextInfoDTO merchantInfoDTO) {
        if(checkSupplierRole(merchantInfoDTO.getTenantId (),merchantInfoDTO.getAuthUserId())){
            return Collections.emptyList ();
        }
        TenantNotifyReq tenantNotifyReq = new TenantNotifyReq();
        tenantNotifyReq.setTenantId(merchantInfoDTO.getTenantId());
        List<MsgNotifySendLogResp> msgNotifySendLogResps = sendLogFacade.queryAlertNotifyMessage(tenantNotifyReq);
        return msgNotifySendLogResps.stream().map(SendLogConvert::sendLogRespToVO).collect(Collectors.toList());
    }
}
