package com.cosfo.manage.order.mapper.payment;

import com.cosfo.manage.order.model.po.payment.Refund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface RefundMapper {

    /**
     * 根据售后单号查询退款单信息
     * @param tenantId
     * @param afterSaleId
     * @return
     */
    Refund selectByAfterSaleId(@Param("tenantId") Long tenantId, @Param("afterSaleId") Long afterSaleId);

    Refund selectByPrimaryKey(Long id);

    Refund selectByRefundNo(String refundNo);

    /**
     * 根据时间查询成功的退款单
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<Refund> selectSuccessByTime(@Param("tenantId") Long tenantId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据时间查询成功的退款单
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    BigDecimal selectSuccessPrice(@Param("tenantId") Long tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);
}