package com.cosfo.manage.order.model.dto;

import com.cosfo.manage.common.model.dto.PageQueryDTO;
import lombok.Data;

import java.util.List;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/16
 */
@Data
public class OrderQueryDTO extends PageQueryDTO {
    /**
     * 租户编号
     */
    private Long tenantId;
    /**
     * 详情页订单导出传订单编号
     */
    private Long orderId;
    /**
     * 订单状态 1-待确认 3-待配送 4-配送中 5-已完成 6-已取消 空-全部
     */
    private Integer status;
    /**
     * 门店类型
     */
    private Integer storeType;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单类型 0无仓订单 1,三方仓订单 2自营仓订单
     */
    private Integer warehouseType;

    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    /**
     * 注册手机号
     */
    private String phone;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 时间查询类型 1-下单时间 2-支付时间 3-配送时间 4-完成时间
     */
    private Integer timeQueryType;

    /**
     * 页码
     */
//    private Integer pageNum;
    /**
     * 分页大小
     */
//    private Integer pageSize;

    /**
     * 店铺Id
     */
    private List<Long> storeIds;

    /**
     * 账户ID
     */
    private List<Long> accountIds;

    /**
     * 是否为空
     */
    private boolean isNull;

    /**
     * 支付方式 数据库是1,线上支付 2,账期 3余额
     */
    private Integer payType;

    /**
     *支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;
    /**
     * 支付方式的code
     */
    private Integer code;

    /**
     * 订单ids
     */
    private List<Long> orderIds;

    /**
     * 商品名称
     */
    private String title;

    /**
     * itemId
     */
    private String itemId;

    /**
     * itemId
     */
    private List<Long> itemIds;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 门店组ids
     */
    private List<Long> merchantStoreGroupIds;

    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 供应商ids
     */
    private List<Long> supplierIds;

    /**
     * 订单明细ids
     */
    private List<Long> orderItemIds;

    /**
     * 订单来源:0：内部系统; 1：openapi调用; 2:总部代下单
     */
    private Integer orderSource;

    /**
     * 计划单编号
     */
    private String planOrderNo;

    /**
     * 0-普通订单,1=组合订单,2=预售订单
     */
    private Integer orderType;

    /**
     * 是否正序排序
     */
    private Boolean sortOrderIdAsc;

    /**
     * 门店编号（客户编号）
     */
    private String storeNo;

    /**
     * SAP订单编号
     */
    private String sapOrderNo;

//    /**
//     * sap客户编号
//     */
//    private String sapCustomerNo;

    /**
     * sap商品编码 18位
     */
    private String sapSkuCode;

    /**
     * SAP物料编码 10位
     */
    private String sapMaterialCode;

    /**
     * 统一编号
     */
    private String uniqueNo;
}
