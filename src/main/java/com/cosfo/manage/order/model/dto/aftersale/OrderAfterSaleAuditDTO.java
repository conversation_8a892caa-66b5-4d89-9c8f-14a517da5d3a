package com.cosfo.manage.order.model.dto.aftersale;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class OrderAfterSaleAuditDTO {

    /**
     * 售后订单编号
     */
    private String afterSaleOrderNo;
    /**
     * 状态 1审核通过，0审核拒绝
     */
    private Integer auditStatus;
    /**
     * 售后金额
     */
    private BigDecimal totalPrice;
    /**
     * 处理结果
     */
    private String handleRemark;

    /**
     * 回收时间
     */
    @JSONField(format = "yyyy-MM-dd")
    private LocalDateTime recycleTime;

    /**
     * 售后数量
     */
    private Integer amount;

    /**
     * 审核人
     */
    private String operatorName;
    /**
     * 责任类型0供应商1品牌方2门店
     */
    private String responsibilityType;


    /**
     * 配送方式 0其他 1物流快递
     */
    private Integer deliveryType;

    /**
     * 物流公司
     */
    private String deliveryCompany;

    /**
     * 配送单号
     */
    private String deliveryNo;

    /**
     * 配送备注，配送方式0其他时传递
     */
    private String remark;


    /**
     * 计划量 （有仓 退货退款 第二次审核 传递）
     */
    private Integer quantity;


    /**
     * 实际入库量（有仓 退货退款 第二次审核 传递）
     */
    private Integer actualQuantity;

    /**
     * 退回地址Id
     */
    private Long returnAddressId;

    /**
     * 退货库存仓no
     */
    private String returnWarehouseNo;

    /**
     * 供应商申请金额
     */
    private BigDecimal supplierApplyPrice;

    /**
     * 供应商实退金额
     */
    private BigDecimal supplierTotalRefundPrice;

    /**
     *  退款凭证
     */
    private String refundReceipt;
}
