package com.cosfo.manage.order.service;

import com.cosfo.manage.common.model.dto.ExcelImportResDTO;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.order.model.dto.*;
import com.cosfo.manage.order.model.vo.*;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/19
 */
public interface OrderBusinessService {
    /**
     * 订单列表
     *
     * @param orderQueryDTO
     * @return
     */
    PageInfo<OrderVO> list(OrderQueryDTO orderQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 订单详情
     *
     * @param orderId
     * @return
     */
    CommonResult<OrderVO> detail(Long orderId);


    /**
     * 导出列表
     *
     * @param orderQueryDTO
     * @param loginContextInfoDTO
     */
    void export(OrderQueryDTO orderQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 待办事项
     *
     * @param loginContextInfoDTO
     * @return
     */
    PendingMatterVO pendingMatter(LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 确认配送
     *
     * @param orderId
     * @param loginContextInfoDTO
     * @return
     */
    ResultDTO confirmDelivery(Long orderId, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询自提信息
     * @param orderNo
     * @param tenantId
     * @return
     */
    ResultDTO querySelfLifting(String orderNo, Long tenantId);

    /**
     * 订单自提
     * @param orderDTO
     * @return
     */
    ResultDTO selfLifting(OrderDTO orderDTO,Boolean selfCommit);

    /**
     * 批量查询
     *
     * @param orderIds
     * @param tenantId
     * @return
     */
    List<OrderDTO> batchQuery(List<Long> orderIds, Long tenantId);

    /**
     * 查询账单信息
     */
    BillOrderInfoDTO queryBillInfo(List<Long> orderIds, Long tenantId);

    /**
     * 查询品牌指定账单周期内门店账期订单
     *
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderDTO> queryBillOrderByStartTimeAndEndTime(Long tenantId, LocalDateTime startTime,LocalDateTime endTime);

    /**
     * 关闭订单
     *
     * @param orderId
     * @param loginContextInfoDTO
     */
    void closeOrder(Long orderId, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 审核通过订单
     * @param loginContextInfoDTO
     */
    void auditOrder(AuditOrderDTO auditOrderDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 订单改单
     * @param changeOrderDTO
     * @param loginContextInfoDTO
     */
    void changeOrder(ChangeOrderDTO changeOrderDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询订单业务操作日志
     * @param orderId
     * @param loginContextInfoDTO
     * @return
     */
    List<OrderBizLogVO> listOrderBizLog(Long orderId, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询是否有待处理售后单
     *
     * @param closeOrderDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<Boolean> queryHavingAuditOrderAfterSale(CloseOrderDTO closeOrderDTO,LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询商品销售库存
     *
     * @param skuIds
     * @param tenantId
     * @retur    */
    List<OrderSkuSaleDTO> querySkuSaleAmount(List<Long> skuIds, Long tenantId);

    /**
     * 根据订单号查询订单信息
     * 
     *
     * @param orderNos
     * @param tenantId
     * @return
     */
    List<OrderDTO> queryByOrderNos(List<String> orderNos, Long tenantId);

    /**
     * 查询订单信息给其他服务
     *
     * @param orderDTO
     * @return
     */
    CommonResult<OrderVO> queryOrderInfoToOtherService(OrderQueryDTO orderDTO);

    /**
     * 查询近几天的商品销售信息
     * @param skuIds
     * @param tenantId
     * @param days
     * @return
     */
    List<OrderSkuSaleDTO> querySkuSaleAmountByDays(List<Long> skuIds, Long tenantId, Integer days);

    /**
     * 查询近几天的商品仓库维度的销售信息
     * @param skuIds
     * @param tenantId
     * @param days
     * @return
     */
    List<OrderSkuSaleDTO> querySkuSaleAmountByDaysWithStoreNo(List<Long> skuIds, Long tenantId, Integer days);

    /**
     * 查询近几天的商品城市维度的销售信息
     * @param skuIds
     * @param tenantId
     * @param days
     * @return
     */
    List<OrderSkuSaleDTO> querySkuSaleAmountByCity(List<Long> skuIds, Long tenantId, Integer days);

    /**
     * 修复有问题订单
     */
    void fixOrder();

    /**
     * 无仓订单立即配送
     * @param orderDeliveryDTO
     * @param loginContextInfoDTO
     */
    boolean orderDelivery(OrderDeliveryDTO orderDeliveryDTO, LoginContextInfoDTO loginContextInfoDTO);


    /**
     * 导入表格批量发货
     *
     * @param tenantId
     * @param file
     * @param loginContextInfoDTO
     * @return
     */
    ExcelImportResDTO importOrderDelivery(Long tenantId, MultipartFile file, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 获取订单详情配送详信息
     * @param orderId
     * @param loginContextInfoDTO
     * @return
     */
    OrderDetailDeliveryVO deliveryDetails(Long orderId, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 修改履约单
     * @param orderDeliveryUpdateDTO
     * @param loginContextInfoDTO
     */
    void updateDelivery(OrderDeliveryUpdateDTO orderDeliveryUpdateDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 查询符合条件的订单数量
     * @param batchDeliveryNotifyDTO
     * @param loginContextInfoDTO
     * @return
     */
    Integer queryNeedDeliveryData(BatchDeliveryNotifyDTO batchDeliveryNotifyDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 批量通知仓库配送
     * @param batchDeliveryNotifyDTO
     */
    OrderBatchNotifyVO batchDeliveryNotify(BatchDeliveryNotifyDTO batchDeliveryNotifyDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 单个订单通知仓库配送
     * @param orderId
     * @param loginContextInfoDTO
     */
    OrderBatchNotifyVO deliveryNotify(Long orderId, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 获取orderId
     * @param orderNo
     * @return
     */
    Long queryOrderSimpleDto(String orderNo);

    OrderVO queryOrderVOByOrderId(Long orderId);

    /**
     * 查询订单列表
     *
     * @param req 查询条件
     * @return 订单列表
     */
    PageInfo<OrderVO> queryOrderPage(OrderQueryReq req);
}
