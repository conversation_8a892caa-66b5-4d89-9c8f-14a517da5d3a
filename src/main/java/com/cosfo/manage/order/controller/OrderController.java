package com.cosfo.manage.order.controller;

import com.cosfo.manage.common.constant.AuthPermissionConstant;
import com.cosfo.manage.common.context.BillPayEnum;
import com.cosfo.manage.common.controller.BaseController;
import com.cosfo.manage.common.model.dto.LoginContextInfoDTO;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.order.controller.rolestrategy.OrderRoleRuleContext;
import com.cosfo.manage.order.model.dto.*;
import com.cosfo.manage.order.model.vo.*;
import com.cosfo.manage.order.service.OrderBusinessService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.xianmu.authentication.common.aspect.TenantPrivilegesPermission;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单管理
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/19
 */
@RestController
@RequestMapping("/order")
public class OrderController extends BaseController {
    @Resource
    private OrderBusinessService orderBusinessService;
    @Resource
    private OrderRoleRuleContext orderRoleRuleContext;

    /**
     * 订单列表
     *
     * @param orderQueryDTO
     * @return
     */
    @RequestMapping(value = "/listAll", method = RequestMethod.POST)
    public CommonResult<PageInfo<OrderVO>> list(@RequestBody OrderQueryDTO orderQueryDTO) {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        Long authUserId = merchantInfoDTO.getAuthUserId();
        PageInfo<OrderVO> list = orderBusinessService.list(orderQueryDTO, merchantInfoDTO);
        PageInfo<OrderVO> result = orderRoleRuleContext.handle(merchantInfoDTO.getTenantId(), authUserId).handlePage(list, authUserId);
        return CommonResult.ok(result);
    }

    /**
     * 订单详情
     *
     * @param orderId
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public CommonResult<OrderVO> detail(Long orderId) {
        return orderBusinessService.detail(orderId);
    }

    /**
     * 导出订单列表
     *
     * @param orderQueryDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(@RequestBody OrderQueryDTO orderQueryDTO) {
        orderBusinessService.export(orderQueryDTO, getMerchantInfoDTO());
    }

//    /**
//     * 今日实况-支付订单数、支付门店数
//     *
//     * @return
//     */
//    @RequestMapping(value = "/condition", method = RequestMethod.GET)
//    public ResultDTO condition() {
//        return orderBusinessService.condition(getMerchantInfoDTO());
//    }

    /**
     * 待办事项
     *
     * @return
     */
    @TenantPrivilegesPermission(expireError = true, permissionCode = "cosfo_manage:dashboard-now:query")
    @RequestMapping(value = "/pending/matter", method = RequestMethod.GET)
    public CommonResult<PendingMatterVO> pendingMatter() {
        return CommonResult.ok(orderBusinessService.pendingMatter(getMerchantInfoDTO()));
    }

    /**
     * 确认配送
     *
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/confirm/delivery", method = RequestMethod.POST)
    public ResultDTO confirmDelivery(@RequestBody OrderQueryDTO orderQueryDTO) {
        return orderBusinessService.confirmDelivery(orderQueryDTO.getOrderId(), getMerchantInfoDTO());
    }

    /**
     * 查询自提信息
     * @param orderNo
     * @return
     */
    @RequestMapping(value = "/query/self-lifting", method = RequestMethod.GET)
    public ResultDTO<List<OrderSelfLiftingDTO>> querySelfLifting(String orderNo) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        Long tenantId = loginContextInfoDTO.getTenantId();
        return orderBusinessService.querySelfLifting(orderNo, tenantId);
    }

    /**
     * 订单自提
     * @param orderDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @RequestMapping(value = "/self-lifting", method = RequestMethod.POST)
    public ResultDTO selfLifting(@RequestBody OrderDTO orderDTO) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        orderDTO.setTenantId(loginContextInfoDTO.getTenantId());
        return orderBusinessService.selfLifting(orderDTO,Boolean.TRUE);
    }

    /**
     * 关单
     *
     * @param closeOrderDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/close-order")
    public CommonResult closeOrder(@RequestBody CloseOrderDTO closeOrderDTO){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        orderBusinessService.closeOrder(closeOrderDTO.getOrderId(), loginContextInfoDTO);
        return CommonResult.ok();
    }


    /**
     * 订单审核
     *
     * @param auditOrderDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/audit-order")
    public CommonResult auditOrder(@RequestBody AuditOrderDTO auditOrderDTO){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        if(auditOrderDTO.getAuditStatus ()) {
            orderBusinessService.auditOrder (auditOrderDTO, loginContextInfoDTO);
        }else{
            orderBusinessService.closeOrder (auditOrderDTO.getOrderId (),loginContextInfoDTO);
        }
        return CommonResult.ok();
    }

    /**
     * 订单改单
     *
     * @param changeOrderDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping(value = "/upsert/change-order")
    public CommonResult changeOrder(@RequestBody @Valid ChangeOrderDTO changeOrderDTO){
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        orderBusinessService.changeOrder(changeOrderDTO, loginContextInfoDTO);
        return CommonResult.ok();
    }


    /**
     * 查询订单的操作记录
     *
     * @param orderId
     * @return
     */
    @PostMapping("/biz-log")
    public CommonResult<List<OrderBizLogVO>> listOrderBizLog(Long orderId) {
        return CommonResult.ok(orderBusinessService.listOrderBizLog(orderId, getMerchantInfoDTO()));
    }


    /**
     * 是否有未关闭订单
     *
     * @param closeOrderDTO
     * @return
     */
    @PostMapping(value = "/query/having-audit-order-after-sale")
    public CommonResult<Boolean> queryHavingAuditOrderAfterSale(@RequestBody CloseOrderDTO closeOrderDTO){
        return orderBusinessService.queryHavingAuditOrderAfterSale(closeOrderDTO, getMerchantInfoDTO());
    }

    /**
     * 无仓订单立即配送
     *
     * @param orderDeliveryDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("upsert/order-delivery")
    public CommonResult<Boolean> orderDelivery(@RequestBody OrderDeliveryDTO orderDeliveryDTO){
        return CommonResult.ok(orderBusinessService.orderDelivery(orderDeliveryDTO, getMerchantInfoDTO()));
    }

    /**
     * 导入表格批量发货
     *
     * @param file 批量发货文件
     * @return
     */
    @PostMapping("batch/import/order-delivery")
    public CommonResult<?> batchImportOrderDelivery(@RequestBody MultipartFile file) {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        return CommonResult.ok(orderBusinessService.importOrderDelivery(merchantInfoDTO.getTenantId(), file, merchantInfoDTO));
    }

    /**
     * 批量通知仓库配送
     *
     * @param batchDeliveryNotifyDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/batch-delivery-notify")
    public CommonResult<OrderBatchNotifyVO> batchDeliveryNotify(@RequestBody @Valid BatchDeliveryNotifyDTO batchDeliveryNotifyDTO){
        return CommonResult.ok(orderBusinessService.batchDeliveryNotify(batchDeliveryNotifyDTO, getMerchantInfoDTO()));
    }

    /**
     * 单个订单通知仓库配送
     *
     * @param orderId
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/delivery-notify")
    public CommonResult<OrderBatchNotifyVO> deliveryNotify(Long orderId){
        return CommonResult.ok(orderBusinessService.deliveryNotify(orderId, getMerchantInfoDTO()));
    }

    /**
     * 查询需要配送数量
     *
     * @param batchDeliveryNotifyDTO
     * @return
     */
    @PostMapping("/query/need-delivery-data")
    public CommonResult<Integer> queryNeedDeliveryData(@RequestBody(required = false) BatchDeliveryNotifyDTO batchDeliveryNotifyDTO){
        return CommonResult.ok(orderBusinessService.queryNeedDeliveryData(batchDeliveryNotifyDTO, getMerchantInfoDTO()));
    }

    /**
     * 查询配送明细
     *
     * @param orderId
     * @return
     */
    @PostMapping("/query/delivery-details")
    public CommonResult<OrderDetailDeliveryVO> deliveryDetails(Long orderId) {
        LoginContextInfoDTO merchantInfoDTO = getMerchantInfoDTO();
        Long authUserId = merchantInfoDTO.getAuthUserId();
        OrderDetailDeliveryVO orderDetailDeliveryVO = orderBusinessService.deliveryDetails(orderId, merchantInfoDTO);
        OrderDetailDeliveryVO result = orderRoleRuleContext.handle(merchantInfoDTO.getTenantId(), authUserId)
                .handleDeliveryDetails(orderDetailDeliveryVO, authUserId);
        return CommonResult.ok(result);
    }

    /**
     * 修改订单履约单信息
     * @param orderDeliveryUpdateDTO
     * @return
     */
    @RequiresRoles(value = {AuthPermissionConstant.SUPER_ROLE_CODE, AuthPermissionConstant.COMMON_ROLE}, logical = Logical.OR)
    @PostMapping("/upsert/delivery")
    public CommonResult<Object> updateDelivery(@RequestBody @Valid OrderDeliveryUpdateDTO orderDeliveryUpdateDTO) {
        orderBusinessService.updateDelivery(orderDeliveryUpdateDTO, getMerchantInfoDTO());
        return CommonResult.ok();
    }

    /**
     * 查询快递公司列表
     * @param expressName 输入名称
     * @return
     */
    @PostMapping("/query/delivery-list")
    public CommonResult<List<String>> queryFastMallList(String expressName) {
        // return CommonResult.ok(orderBusinessService.queryFastMallList(expressName));
        return CommonResult.ok(Lists.newArrayList("新竹物流"));
    }

    /**
     * 根据订单编号查询订单ID
     *
     * @param orderNo
     * @return
     */
    @RequestMapping(value = "/query-order", method = RequestMethod.GET)
    public CommonResult<Long> queryOrderSimpleDto(String orderNo) {
        return CommonResult.ok(orderBusinessService.queryOrderSimpleDto(orderNo));
    }
}
