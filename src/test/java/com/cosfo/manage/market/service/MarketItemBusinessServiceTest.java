package com.cosfo.manage.market.service;

import com.alibaba.fastjson.JSON;
import com.cofso.item.client.req.MarketInputReq;
import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;
import com.cosfo.manage.common.result.ResultDTO;
import com.cosfo.manage.downloadcenter.item.ItemImportHandler;
import com.cosfo.manage.downloadcenter.item.MarketExcelDataDTO;
import com.cosfo.manage.market.controller.MarketClassificationController;
import com.google.common.collect.Lists;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class MarketItemBusinessServiceTest {

    @Resource
    private MarketItemBusinessService marketItemBusinessService;
    @Resource
    private MarketClassificationController marketClassificationController;
    @Test
    void selectSaleStatusBySkuIds() {
        List<MarketItemOnSaleSimple4StoreResp> marketItemOnSaleSimple4StoreResps = marketItemBusinessService.selectSaleStatusBySkuIds(2L, Lists.newArrayList(10193L));
        System.out.println(marketItemOnSaleSimple4StoreResps);
    }

    @Test
    public void test() {

        ResultDTO resultDTO = marketClassificationController.listAllById(1000L);
        System.out.println(resultDTO);
    }

    @Resource
    ItemImportHandler itemImportHandler;

    @Test
    public void test2() {
        String json = "{\"bizResult\":\"{\\\"failedCount\\\":0,\\\"successCount\\\":1,\\\"totalCount\\\":1}\",\"bizStatus\":\"SUCCESS\",\"fileName\":\"导入错误_商品导入模板 (5).xlsx\",\"resId\":10267,\"status\":\"UPLOADED\"}\\\"unit\\\":1}]\",\"strategyType\":2,\"targetIds\":[10000],\"targetType\":0}],\"sapMaterialCode\":\"1\",\"sapSkuCode\":\"1\",\"specificationUnit\":\"1\",\"tenantId\":10000},\"tenantId\":10000,\"title\":\"1\"}/sass-manage/test/oss/商品(1)-c29793a50b0542878954e0d7750270f8.xls\",\"importMaxRow\":500,\"resId\":10059,\"source\":\"saas\",\"userBase\":{\"bizUserId\":10001,\"id\":10001,\"nickname\":\"peng.tang\",\"phone\":\"18618107293\",\"roleIds\":[10000,119,10000,10001],\"systemOrigin\":6,\"tenantId\":10000,\"userBaseId\":10001,\"username\":\"18618107293\"}}";
//        new ArrayList<M>()
//        itemImportHandler.dealExcelData();

    }


}